package com.ruoyi.system.domain.dto;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HomeScreenDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty( "排序方式")
    private String sortType;

    @ApiModelProperty("语言类型")
    private String lang;

    @ApiModelProperty( "标签id 例如：包袋id")
    private String menuId;

    @ApiModelProperty( "类别ids")
    private List<String> kindList;

    @ApiModelProperty( "材质ids")
    private  List<String> materialList;

    @ApiModelProperty( "颜色")
    private List<String> colorList;

    @ApiModelProperty( "品相")
    private List<String> appearanceList;

    @ApiModelProperty( "标签")
    private String labelId;

    @ApiModelProperty("产品名称或者别名")
    private String productName;

}
