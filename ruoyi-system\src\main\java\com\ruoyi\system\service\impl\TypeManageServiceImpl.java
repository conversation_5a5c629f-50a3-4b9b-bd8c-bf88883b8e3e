package com.ruoyi.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.ManageTypeEnum;
import com.ruoyi.common.enums.StatusEnum;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.resolver.TencentTranslationClient;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.MenuDetail;
import com.ruoyi.system.entity.ProductDetail;
import com.ruoyi.system.entity.TypeManage;
import com.ruoyi.system.mapper.MenuDetailMapper;
import com.ruoyi.system.mapper.ProductDetailMapper;
import com.ruoyi.system.mapper.TypeManageMapper;
import com.ruoyi.system.service.TypeManageService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.StringUtils.getUUID;
import static com.ruoyi.common.utils.StringUtils.isBlankString;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Service
public class TypeManageServiceImpl extends ServiceImpl<TypeManageMapper, TypeManage> implements TypeManageService {
  @Autowired
  private MenuDetailMapper menuDetailMapper;
  @Autowired
  ProductDetailMapper productDetailMapper;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean saveTypeManage(final TypeManage typeManage) throws TencentCloudSDKException, InterruptedException {
    boolean save = false;
    typeManage.setLangType(LanguageEnum.CHINA.getValue());
    if (typeManage == null) {
      throw new ServiceException("数据有误，请确认后再试", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    if (isBlankString(typeManage.getManageType())) {
      throw new ServiceException("数据有误，请确认后再试", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    if (typeManage.getManageType().equals(ManageTypeEnum.MATERIAL.getCode()) && isBlankString(typeManage.getMenuId())) {
      throw new ServiceException("新增材质，父级不能为空", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    ;
    final QueryWrapper<TypeManage> eq = new QueryWrapper<TypeManage>()
        .eq("manage_type", typeManage.getManageType());
    if (typeManage.getManageType().equals(ManageTypeEnum.MATERIAL.getCode())) {
      eq.eq("name", typeManage.getName()).eq("menu_id", typeManage.getMenuId());
      final TypeManage typeManageOther = baseMapper.selectOne(eq);
      if (typeManageOther != null && typeManage.getTypeId() == null) {
        throw new ServiceException("当前类别下材质已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      } else if (typeManageOther != null && !typeManage.getTypeId().equals(typeManageOther.getTypeId())) {
        throw new ServiceException("当前类别下材质已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      }
    } else if (typeManage.getManageType().equals(ManageTypeEnum.COLOR.getCode())) {
      eq.eq("name", typeManage.getName());
      //颜色名称 新增
      final TypeManage typeManages = baseMapper.selectOne(eq);

      if (typeManages != null && typeManage.getTypeId() == null) {
        throw new ServiceException("当前颜色名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
        //颜色名称 编辑
      } else if (typeManages != null && typeManage.getTypeId() != null && !typeManage.getTypeId().equals(typeManages.getTypeId())) {
        throw new ServiceException("当前颜色名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      }
      final QueryWrapper<TypeManage> queryWrapper = new QueryWrapper<TypeManage>()
          .eq("manage_type", typeManage.getManageType())
          .eq("color_value", typeManage.getColorValue())
          .eq("lang_type", LanguageEnum.CHINA.getValue());
      final TypeManage getColorValue = baseMapper.selectOne(queryWrapper);

      //色值新增
      if (getColorValue != null && typeManage.getTypeId() == null) {
        throw new ServiceException("当前色值已存在", StatusEnum.SERVICE_ERROR.getResultCode());
        //色值 编辑
      } else if (getColorValue != null && typeManage.getTypeId() != null && !typeManage.getTypeId().equals(getColorValue.getTypeId())) {
        throw new ServiceException("当前色值已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      }

    } else if (typeManage.getManageType().equals(ManageTypeEnum.APPEARANCE.getCode())) {
      eq.eq("name", typeManage.getName());
      final TypeManage typeManageOther = baseMapper.selectOne(eq);
      if (typeManageOther != null && typeManage.getTypeId() == null) {
        throw new ServiceException("当前品相名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      } else if (typeManageOther != null && !typeManage.getTypeId().equals(typeManageOther.getTypeId())) {
        throw new ServiceException("当前品相名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());

      }
    } else {
      throw new ServiceException("该名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
    }


    if (typeManage.getManageType().equals("material") && !isBlankString(typeManage.getMenuId())) {
      final QueryWrapper<MenuDetail> menuDetailQueryWrapper = new QueryWrapper<MenuDetail>().eq("MENU_ID", typeManage.getMenuId()).eq("lang_type", "zh");
      final MenuDetail menuDetail = menuDetailMapper.selectOne(menuDetailQueryWrapper);
      typeManage.setMenuName(menuDetail.getName());
    }
    if (isBlankString(typeManage.getTypeId())) {

      typeManage.setTypeId(getUUID());
      typeManage.setCreatedTime(new Date());
      typeManage.setUpdatedTime(new Date());
      if (typeManage.getManageType().equals(ManageTypeEnum.MATERIAL.getCode()) || typeManage.getManageType().equals(ManageTypeEnum.COLOR.getCode())) {
        typeManage.setName(typeManage.getNameZh());
      }
      final TypeManage tranEnslation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.EN.getValue(), typeManage);
      final TypeManage tranJaslation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.JA.getValue(), typeManage);

      if (typeManage.getManageType().equals(ManageTypeEnum.MATERIAL.getCode()) || typeManage.getManageType().equals(ManageTypeEnum.COLOR.getCode())) {
        tranEnslation.setName(typeManage.getNameEn());
        tranJaslation.setName(typeManage.getNameJa());
      }

      baseMapper.insert(typeManage);
      baseMapper.insert(tranEnslation);
      save = baseMapper.insert(tranJaslation) > 0;
      return save;
    } else {
      typeManage.setUpdatedTime(new Date());
      final QueryWrapper<TypeManage> queryWrapper = new QueryWrapper<TypeManage>().eq("type_id", typeManage.getTypeId()).eq("manage_type", typeManage.getManageType());
      final List<TypeManage> list = baseMapper.selectList(queryWrapper);
      for (final TypeManage typeManage1 : list) {
        final String id = typeManage1.getId();
        if (typeManage1.getLangType().equals("zh")) {
          if (typeManage.getManageType().equals(ManageTypeEnum.MATERIAL.getCode()) || typeManage.getManageType().equals(ManageTypeEnum.COLOR.getCode())) {
            typeManage.setName(typeManage.getNameZh());
          }
          BeanUtils.copyProperties(typeManage, typeManage1);
        } else {
          final TypeManage translation = translation(LanguageEnum.CHINA.getValue(), typeManage1.getLangType(), typeManage);
          if (typeManage.getManageType().equals(ManageTypeEnum.MATERIAL.getCode()) || typeManage.getManageType().equals(ManageTypeEnum.COLOR.getCode())) {
            if ( LanguageEnum.EN.getValue().equals(typeManage1.getLangType())){
              translation.setName(typeManage.getNameEn());
            } else if ( LanguageEnum.JA.getValue().equals(typeManage1.getLangType())){
              translation.setName(typeManage.getNameJa());
            }
          }

          BeanUtils.copyProperties(translation, typeManage1);
        }
        //修改
        typeManage1.setId(id);
        if (typeManage.getSort() == null) {
          baseMapper.updateSort(typeManage.getTypeId());
          typeManage1.setSort(null);
        } else {
          typeManage1.setSort(typeManage.getSort());
        }
        typeManage1.setSort(typeManage1.getSort());
        save = baseMapper.updateById(typeManage1) > 0;
      }
      return save;
    }
  }


  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean deleteTypeManage(final String typeId) {
    final List<ProductDetail> color = productDetailMapper.selectList(new QueryWrapper<ProductDetail>().eq("color", typeId));
    if (color.size() > 0) {
      throw new ServiceException("当前有商品存在该颜色，请先修改或删除商品", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final List<ProductDetail> appearance = productDetailMapper.selectList(new QueryWrapper<ProductDetail>().eq("appearance", typeId));

    if (appearance.size() > 0) {
      throw new ServiceException("当前有商品存在该品相，请先修改或删除商品", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final List<ProductDetail> material = productDetailMapper.selectList(new QueryWrapper<ProductDetail>().eq("material", typeId));
    if (material.size() > 0) {
      throw new ServiceException("当前有商品存在该材质，请先修改或删除商品", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    return baseMapper.delete(new QueryWrapper<TypeManage>().eq("type_id", typeId)) > 0;
  }

  @Override
  public List<TypeManage> findTypeManageByPage(final String menuId, final String manageType, final String name) {
    if (isBlankString(manageType)) {
      throw new ServiceException("数据有误，请确认后再试", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final QueryWrapper<TypeManage> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .like(StringUtils.isNotBlank(name), "name", name)
        .like(StringUtils.isNotBlank(menuId), "menu_id", menuId)
        .eq("manage_type", manageType)
        .eq("lang_type", LanguageEnum.CHINA.getValue())
        .orderByAsc("sort IS NULL")
        .orderByAsc("sort")
        .orderByDesc("updated_time").orderByDesc("created_time");
    return baseMapper.selectList(queryWrapper);

  }

  @Override
  public TypeManage getTypeManage(final String typeId, String lang) {
    if (isBlankString(lang)) {
      lang = LanguageEnum.CHINA.getValue();
    }
    TypeManage zh = baseMapper.selectOne(new QueryWrapper<TypeManage>().eq("type_id", typeId).eq("lang_type", lang));
    TypeManage en = baseMapper.selectOne(new QueryWrapper<TypeManage>().eq("type_id", typeId).eq("lang_type", LanguageEnum.EN.getValue()));
    TypeManage ja = baseMapper.selectOne(new QueryWrapper<TypeManage>().eq("type_id", typeId).eq("lang_type", LanguageEnum.JA.getValue()));
    zh.setNameZh(zh.getName());
    zh.setNameEn(en.getName());
    zh.setNameJa(ja.getName());
    return zh;
  }

  @Override
  public List<TopMenuVO> getTypeList(final String menuId, final String manageType) {
    final List<TopMenuVO> typeListVOS = new ArrayList<>();
    List<TypeManage> typeManages = new ArrayList<>();
    if (manageType.equals(ManageTypeEnum.MATERIAL.getCode())) {
      final List<TopMenuVO> topMenuVOS = baseMapper.selectMenuList();
      for (final TopMenuVO topMenuVO : topMenuVOS) {
        final List<TopMenuVO> topMenuChildVO = baseMapper.selectChildList(topMenuVO.getValue());
        if (topMenuChildVO.size() > 0) {
          topMenuVO.setChildren(topMenuChildVO);
        }
      }
      List<TopMenuVO> collect = topMenuVOS.stream().distinct().collect(Collectors.toList());
      return collect;
    } else {
      typeManages = baseMapper.selectList(new QueryWrapper<TypeManage>().eq("manage_type", manageType).eq("lang_type", "zh").orderByAsc("sort IS NULL")
              .orderByAsc(true, "sort")
              .orderByDesc("updated_time")
              .orderByDesc("created_time"));
    }
    for (final TypeManage typeManage1 : typeManages) {
      final TopMenuVO typeListVO = new TopMenuVO();
      typeListVO.setLabel(typeManage1.getName());
      typeListVO.setValue(typeManage1.getTypeId());
      typeListVOS.add(typeListVO);
    }
    return typeListVOS;
  }

  @Override
  public List<TopMenuVO> getMenuList() {
    final List<TopMenuVO> topMenuVOS = new ArrayList<>();
    final List<MenuDetail> dataList = menuDetailMapper.selectList(new QueryWrapper<MenuDetail>().eq("lang_type", "zh").orderByAsc("sort IS NULL")
            .orderByAsc(true, "sort")
            .orderByDesc("updated_time")
            .orderByDesc("created_time"));
    for (final MenuDetail menuDetail : dataList) {
      if (menuDetail.getIschild().intValue() == 1) {
        final TopMenuVO topMenuVO = new TopMenuVO();
        topMenuVO.setLabel(menuDetail.getName());
        topMenuVO.setValue(menuDetail.getMenuId());
        final List<TopMenuVO> childMenuData = getChildMenuData(menuDetail.getMenuId(), dataList);
        if (childMenuData.size() > 0) {
          topMenuVO.setChildren(childMenuData);
          topMenuVOS.add(topMenuVO);
        }

      }
    }
    return topMenuVOS;
  }

  private List<TopMenuVO> getChildMenuData(final String menuId, final List<MenuDetail> dataList) {
    final List<TopMenuVO> list = new ArrayList<>();
    for (final MenuDetail mod : dataList) {
      if (mod.getIschild() == 2 && mod.getParentId().equals(menuId)) {
        final TopMenuVO topMenuVO = new TopMenuVO();
        topMenuVO.setLabel(mod.getName());
        topMenuVO.setValue(mod.getMenuId());
        list.add(topMenuVO);
      }
    }
    return list;
  }

  TypeManage translation(final String sourceLang, final String targetLang, final TypeManage typeSource) throws
      TencentCloudSDKException, InterruptedException {
    final TencentTranslationClient tencentTranslationClient = new TencentTranslationClient();
    final TypeManage typeTarget = new TypeManage();
    BeanUtils.copyProperties(typeSource, typeTarget);
    typeTarget.setName(isBlankString(typeSource.getName()) ? null : tencentTranslationClient.translateText(typeSource.getName(), sourceLang, targetLang));
    typeTarget.setDescrition(isBlankString(typeSource.getDescrition()) ? null : tencentTranslationClient.translateText(typeSource.getDescrition(), sourceLang, targetLang));
    typeTarget.setColorValue(isBlankString(typeSource.getColorValue()) ? null : tencentTranslationClient.translateText(typeSource.getColorValue(), sourceLang, targetLang).replaceAll(" ", ""));
    typeTarget.setId(null);
    typeTarget.setTypeId(typeSource.getTypeId());
    typeTarget.setLangType(targetLang);
    return typeTarget;
  }
}
