package com.ruoyi.web.controller.system;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.system.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController

public class SysLoginController {
  @Autowired
  private SysLoginService loginService;

  @Autowired
  private ISysMenuService menuService;

  @Autowired
  private SysPermissionService permissionService;

  /**
   * 登录方法
   *
   * @param loginBody 登录信息
   * @return 结果
   */
  @PostMapping("/login")
  public AjaxResult login(@RequestBody final LoginBody loginBody) {
    final AjaxResult ajax = AjaxResult.success();
    // 生成令牌
    final String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
        loginBody.getUuid());
    ajax.put(Constants.TOKEN, token);
    return ajax;
  }

  /**
   * 获取用户信息
   *
   * @return 用户信息
   */
  @GetMapping("getInfo")
  public AjaxResult getInfo() {
    final SysUser user = SecurityUtils.getLoginUser().getUser();
    // 角色集合
    final Set<String> roles = permissionService.getRolePermission(user);
    // 权限集合
    final Set<String> permissions = permissionService.getMenuPermission(user);
    final AjaxResult ajax = AjaxResult.success();
    ajax.put("user", user);
    ajax.put("roles", roles);
    ajax.put("permissions", permissions);
    return ajax;
  }

  /**
   * 获取路由信息
   *
   * @return 路由信息
   */
  @GetMapping("getRouters")
  public AjaxResult getRouters() {
    final Long userId = SecurityUtils.getUserId();
    final List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
    return AjaxResult.success(menuService.buildMenus(menus));
  }
}
