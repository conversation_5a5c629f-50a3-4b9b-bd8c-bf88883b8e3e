package com.ruoyi.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.ManageTypeEnum;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import com.ruoyi.system.domain.dto.HomeScreenDTO;
import com.ruoyi.system.domain.vo.ProductDetailVO;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.FileDetail;
import com.ruoyi.system.entity.MenuDetail;
import com.ruoyi.system.entity.ProductDetail;
import com.ruoyi.system.entity.TypeManage;
import com.ruoyi.system.mapper.FileDetailMapper;
import com.ruoyi.system.mapper.MenuDetailMapper;
import com.ruoyi.system.mapper.ProductDetailMapper;
import com.ruoyi.system.mapper.TypeManageMapper;
import com.ruoyi.system.service.GateWayService;
import com.ruoyi.system.service.IFileDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GateWayServiceImpl implements GateWayService {


  @Autowired
  private MenuDetailMapper menuDetailMapper;
  @Autowired
  private ProductDetailMapper productDetailMapper;
  @Autowired
  private TypeManageMapper typeManageMapper;
  @Autowired
  private FileDetailMapper fileDetailMapper;
  @Autowired
  private IFileDetailService fileDetailService;


  @Override
  public R<List<Map<String, Object>>> productListTop(final String lang) {
    //menuId为空则获取全部,存在与则获取该标签下所有数据
    final List<MenuDetail> menuDetails = menuDetailMapper.selectList(new QueryWrapper<MenuDetail>().eq("lang_type", lang).orderByAsc("sort IS NULL")
            .orderByAsc(true, "sort")
            .orderByDesc("updated_time")
            .orderByDesc("created_time"));
    final List<MenuDetail> parentMenuList = menuDetails.stream().filter(obj -> StringUtils.isBlank(obj.getParentId())).collect(Collectors.toList());
    //迭代处理数据
    final List<Map<String, Object>> returnInfos = new ArrayList<>();
    for (final MenuDetail menuDetail : parentMenuList) {
      final Map<String, Object> returnInfo = new LinkedHashMap<>();
      returnInfo.put("label", menuDetail.getName());
      returnInfo.put("value", menuDetail.getMenuId());
      final List<TopMenuVO> children = getChildren(menuDetail.getMenuId(), menuDetails);
      //类别
      returnInfo.put(ManageTypeEnum.CATEGORY.getCode(), children);
      //品相
      final List<TypeManage> typeManages = typeManageMapper.selectList(new QueryWrapper<TypeManage>()
          .eq("manage_type", ManageTypeEnum.APPEARANCE.getCode())
          .eq("lang_type", lang).orderByAsc("sort"));
      final List<Map<String, String>> maps = new ArrayList<>();
      for (final TypeManage typeManage : typeManages) {
        final Map<String, String> map = new HashMap<>();
        map.put("label", typeManage.getName());
        map.put("value", typeManage.getTypeId());
        maps.add(map);
      }
      returnInfo.put(ManageTypeEnum.APPEARANCE.getCode(), maps);
      returnInfos.add(returnInfo);
    }
    return R.ok(returnInfos);
  }

  /**
   * 首页筛选框
   *
   * @param lang
   * @param menuId
   * @return
   */
  @Override
  public R<Map<String, Object>> screenList(final String lang, final String menuId) {
    final Map<String, Object> returnScreenList = new LinkedHashMap<>();
    //类别
    if (menuId != null && !menuId.isEmpty()) {
      List<MenuDetail> children = menuDetailMapper.selectChildrenByParentId(menuId,lang);
      returnScreenList.put(ManageTypeEnum.CATEGORY.getCode(), getChildren(menuId,children));
    }
    //材质
    QueryWrapper<TypeManage> queryWrapper = new QueryWrapper<TypeManage>()
            .eq("manage_type", ManageTypeEnum.MATERIAL.getCode())
            .eq("lang_type", lang).orderByAsc("sort");

    if (menuId != null && !menuId.isEmpty()) {
      queryWrapper.eq("menu_id", menuId);
    }
    List<TypeManage> czTypeManages = typeManageMapper.selectList(queryWrapper);
    final List<Map<String, String>> czs = new ArrayList<>();
    for (final TypeManage typeManage : czTypeManages) {
      final Map<String, String> map = new HashMap<>();
      map.put("label", typeManage.getName());
      map.put("value", typeManage.getTypeId());
      czs.add(map);
    }
    returnScreenList.put(ManageTypeEnum.MATERIAL.getCode(),czs);
    //颜色
    List<TypeManage> typeManages = typeManageMapper.selectList(new QueryWrapper<TypeManage>().eq("manage_type", ManageTypeEnum.COLOR.getCode()).eq("lang_type", lang).orderByAsc("sort"));
    final List<Map<String, String>> colors = new ArrayList<>();
    for (final TypeManage typeManage : typeManages) {
      final Map<String, String> map = new HashMap<>();
      map.put("label", typeManage.getName());
      map.put("value", typeManage.getTypeId());
      map.put("colorValue",typeManage.getColorValue());
      colors.add(map);
    }
    returnScreenList.put(ManageTypeEnum.COLOR.getCode(), colors);
    //品相等级SWS
    typeManages = typeManageMapper.selectList(new QueryWrapper<TypeManage>().eq("manage_type", ManageTypeEnum.APPEARANCE.getCode()).eq("lang_type", lang).orderByAsc("sort"));
    final List<Map<String, String>> appearanceFile = new ArrayList<>();
    for (final TypeManage typeManage : typeManages) {
      final Map<String, String> map = new HashMap<>();
      map.put("label", typeManage.getName());
      map.put("value", typeManage.getTypeId());
      appearanceFile.add(map);
    }
    returnScreenList.put(ManageTypeEnum.APPEARANCE.getCode(), appearanceFile);
    //返回数据
    return R.ok(returnScreenList);
  }

  @Override
  public List<ProductDetailVO> productList(final HomeScreenDTO homeScreenDTO) {
    List<ProductDetailVO> productDetailVOS = new ArrayList<>();
    List<ProductDetail> productDetails = productDetailMapper.selectProductList(homeScreenDTO);
    for (ProductDetail productDetail : productDetails) {
      ProductDetailVO productDetailVO = new ProductDetailVO();
      BeanUtils.copyProperties(productDetail,productDetailVO);
      //对应语种价格
      if (homeScreenDTO.getLang().equals(LanguageEnum.CHINA.getValue())){
          productDetailVO.setPrice(productDetail.getChinaPrice());
      } else  if (homeScreenDTO.getLang().equals(LanguageEnum.JA.getValue())){
        productDetailVO.setPrice(productDetail.getJapPrice());
      } else  if (homeScreenDTO.getLang().equals(LanguageEnum.EN.getValue())){
        productDetailVO.setPrice(productDetail.getEngPrice());
      }
      //同意返回日文金额
      productDetailVO.setPrice(productDetail.getJapPrice());
      //超级单品产品图
        List<FileDetail> fileDetails = fileDetailService.filesByProductId(productDetail.getProductId(), 4);
        List<FileDetailChilDto> fileDetailChilDtos = new ArrayList<>();
        for (FileDetail fileDetail : fileDetails) {
          FileDetailChilDto fileDetailChilDto = new FileDetailChilDto();
          fileDetailChilDto.setId(fileDetail.getId());
          fileDetailChilDto.setUrl(fileDetail.getFileUrl());
          fileDetailChilDtos.add(fileDetailChilDto);
        }
      productDetailVO.setSuperProdrctFileUrls(fileDetailChilDtos);
      //类别名称
      MenuDetail menuDetail = menuDetailMapper.selectOne(new QueryWrapper<MenuDetail>().eq("menu_id", productDetail.getCategory()).eq("lang_type", homeScreenDTO.getLang()));
      if (menuDetail!=null) {
        productDetailVO.setCategoryName(menuDetail.getName());
      }
      //材质
      TypeManage material = typeManageMapper.selectOne(new QueryWrapper<TypeManage>().eq("type_id", productDetail.getMaterial()).eq("lang_type", homeScreenDTO.getLang()));
      if (material!=null) {
        productDetailVO.setMaterialName(material.getName());
      }
      //品相
      TypeManage appearance= typeManageMapper.selectOne(new QueryWrapper<TypeManage>().eq("type_id", productDetail.getAppearance()).eq("lang_type", homeScreenDTO.getLang()));
      if (appearance!=null) {
        productDetailVO.setAppearanceName(appearance.getName());
      }
      //颜色
      TypeManage color= typeManageMapper.selectOne(new QueryWrapper<TypeManage>().eq("type_id", productDetail.getColor()).eq("lang_type", homeScreenDTO.getLang()));
      if (color!=null) {
        productDetailVO.setColorName(color.getName());
      }
      List<FileDetail> fileDetailss = fileDetailMapper.selectList(new QueryWrapper<FileDetail>().eq("product_id", productDetail.getProductId()).eq("is_carousel", 0).orderByAsc("created_time"));
      List<FileDetailChilDto> fileDetailChilDtoss = new ArrayList<>();
      for (FileDetail fileDetail : fileDetailss) {
        FileDetailChilDto fileDetailChilDto = new FileDetailChilDto();
        fileDetailChilDto.setId(fileDetail.getId());
        fileDetailChilDto.setUrl(fileDetail.getFileUrl());
        fileDetailChilDtoss.add(fileDetailChilDto);
      }
      productDetailVO.setFileUrls(fileDetailChilDtoss);
      List<FileDetail> supperFileDetails = fileDetailMapper.selectList(new QueryWrapper<FileDetail>().eq("product_id", productDetail.getProductId()).eq("is_carousel", 3));
      List<String> collect = supperFileDetails.stream().map(FileDetail::getFileUrl).collect(Collectors.toList());
      productDetailVO.setSupperFiles(collect);

      productDetailVOS.add(productDetailVO);
    }
    return productDetailVOS;
  }

  private List<TopMenuVO> getChildren(final String parentId, final List<MenuDetail> menuDetails) {
    final List<TopMenuVO> children = new ArrayList<>();
    for (final MenuDetail menuDetail : menuDetails) {
      if (parentId.equals(menuDetail.getParentId())) {
        final TopMenuVO childVO = new TopMenuVO();
        childVO.setLabel(menuDetail.getName());
        childVO.setValue(menuDetail.getMenuId());
        // 递归处理子节点的子节点
        childVO.setChildren(getChildren(menuDetail.getMenuId(), menuDetails));
        children.add(childVO);
      }
    }
    return children;
  }

}
