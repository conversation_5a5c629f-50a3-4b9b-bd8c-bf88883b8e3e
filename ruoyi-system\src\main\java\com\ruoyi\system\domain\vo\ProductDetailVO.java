package com.ruoyi.system.domain.vo;


import com.ruoyi.system.entity.ProductDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 商品详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductDetailVO extends ProductDetail implements Serializable {

  private static final long serialVersionUID = 1L;

  private List<String> fileUrl;

  private String price;

  @ApiModelProperty("超级单品图")
  private List<String> supperFiles;

  @ApiModelProperty("类别名称")
  private String categoryName;
  @ApiModelProperty("颜色名称")
  private String colorName;
  @ApiModelProperty("品相名称")
  private String appearanceName;
  @ApiModelProperty("材质名称")
  private String materialName;
  @ApiModelProperty("是否展示")
  private String isShowName;

}




