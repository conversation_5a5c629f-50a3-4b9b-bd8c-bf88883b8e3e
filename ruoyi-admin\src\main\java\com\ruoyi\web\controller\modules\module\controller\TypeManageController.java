package com.ruoyi.web.controller.modules.module.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.TypeManage;
import com.ruoyi.system.service.TypeManageService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@RestController
@RequestMapping("/type-manage")
@Api(tags = "分类管理", description = "分类管理 material-材质管理 color-颜色管理 appearance-品相管理")
public class TypeManageController extends BaseController {

  @Autowired
  TypeManageService typeManageService;

  @PostMapping("/saveOrUpdate")
  @ApiOperation(value = "新增或者修改材质/颜色/品相")
  public R<Boolean> saveTypeManage(@RequestBody final TypeManage typeManage) throws TencentCloudSDKException, InterruptedException {
    if (typeManageService.saveTypeManage(typeManage)) {
      return R.ok();
    } else {
      return R.fail("操作失败");
    }
  }

  @DeleteMapping("/delete/{id}")
  @ApiOperation(value = "删除材质/颜色/品相")
  public R<Boolean> deleteTypeManage(@PathVariable final String id) throws TencentCloudSDKException, InterruptedException {
    if (typeManageService.deleteTypeManage(id)) {
      return R.ok();
    } else {
      return R.fail("操作失败");
    }
  }

  @GetMapping("/findTypeManageByPage")
  @ApiOperation(value = "材质/颜色/品相 分页查询详情")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "manageType", value = "管理种类", required = false, dataType = "String"),
      @ApiImplicitParam(name = "menuName", value = "一级类别", required = false, dataType = "String"),
      @ApiImplicitParam(name = "typeName", value = "类别名称", required = false, dataType = "String"),})
  public R<TableDataInfo> findTypeManageByPage(
      final String menuId,
      final String manageType,
      final String name) {
    startPage();
    final List<TypeManage> productDetailByPage = typeManageService.findTypeManageByPage(menuId, manageType, name);
    final TableDataInfo dataTable = getDataTable(productDetailByPage);
    if (productDetailByPage != null) {
      return R.ok(dataTable);
    } else {
      return R.fail("操作失败");
    }
  }


  @GetMapping("/getTypeManage")
  @ApiOperation(value = "查询材质/颜色/品相详情")
  public R<TypeManage> getTypeManage(final String typeId, final String lang) throws TencentCloudSDKException, InterruptedException {
    final TypeManage typeManage = typeManageService.getTypeManage(typeId, lang);
    if (typeManage != null) {
      return R.ok(typeManage);
    } else {
      return R.fail("操作失败");
    }
  }

  @GetMapping("/getTypeList")
  @ApiOperation(value = "下拉列表")
  @Anonymous
  @ApiImplicitParams({
      @ApiImplicitParam(name = "menuId", value = "一级类别id", required = false, dataType = "String"),
      @ApiImplicitParam(name = "manageType", value = "分类管理 material-材质管理 color-颜色管理 appearance-品相管理", required = false, dataType = "String")})
  public R<List<TopMenuVO>> getTypeList(final String menuId, final String manageType) {
    final List<TopMenuVO> typeManageList = typeManageService.getTypeList(menuId, manageType);
    if (typeManageList != null) {
      return R.ok(typeManageList);
    } else {
      return R.fail("操作失败");
    }
  }

  @GetMapping("/getMenuList")
  @ApiOperation(value = "类别一二级下拉列表")
  public R<List<TopMenuVO>> getMenuList() {
    final List<TopMenuVO> typeManageList = typeManageService.getMenuList();
    if (typeManageList != null) {
      return R.ok(typeManageList);
    } else {
      return R.fail("操作失败");
    }
  }

}

