package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.dto.TranslatedReqVO;
import com.ruoyi.system.domain.vo.ProductDetailVO;
import com.ruoyi.system.entity.ProductDetail;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

import java.util.List;

/**
 * <p>
 * 商品详情表  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface IProductDetailService extends IService<ProductDetail> {
  /**
   * 商品操作页默认中文，暂时不支持修改其他语言
   *
   * @param productDetailZh
   * @return
   */
  public Boolean saveOrUpdateProductDetail(ProductDetail productDetailZh) throws TencentCloudSDKException, InterruptedException;

  public Boolean removeProductDetailById(String id);

  public List<ProductDetailVO> getProductDetailByPage(String productDetailZhname, String color, String appearance, String material, String category, Integer isShow);

  String japanToChinese(TranslatedReqVO req);

  ProductDetailVO findAllProductDetail(String productId, String langType);

  ProductDetailVO getProductTypeDetail(String id, String langType);
}
