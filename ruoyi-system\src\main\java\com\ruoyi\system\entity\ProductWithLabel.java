package com.ruoyi.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商品与标签关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@TableName("product_with_label")
@ApiModel(value = "ProductWithLabel对象", description = "商品与标签关联表")
public class ProductWithLabel implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  @TableField("label_id")
  private String labelId;

  @TableField("product_id")
  private String productId;


}




