package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.TypeManage;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
public interface TypeManageService extends IService<TypeManage> {

  boolean saveTypeManage(TypeManage typeManage) throws TencentCloudSDKException, InterruptedException;

  boolean deleteTypeManage(String typeId);

  List<TypeManage> findTypeManageByPage(String menuId, String manageType, String name);

  TypeManage getTypeManage(String typeId, String lang);

  List<TopMenuVO> getTypeList(String menuId, String manageType);

  List<TopMenuVO> getMenuList();


}
