<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MenuDetailMapper">


    <select id="selectChildrenByParentId" resultType="com.ruoyi.system.entity.MenuDetail">
        WITH RECURSIVE MenuCTE AS (
            -- 基础查询：选择所有直接子项
            SELECT menu_id, parent_id, name
            FROM menu_detail
            WHERE parent_id =
                       #{parentId}
                       and
                       lang_type
                       =
                       #{langType}
                       UNION ALL
                       SELECT md.menu_id, md.parent_id, md.name
                       FROM menu_detail md
                           INNER JOIN MenuCTE cte
                       ON md.parent_id = cte.menu_id
                           )
                       SELECT *
                       FROM MenuCTE;
    </select>

    <select id="selectMenuName" resultType="java.lang.String">
        select name
        from menu_detail
        where MENU_ID = #{menuId}
          and lang_type = 'zh'
    </select>

    <update id="updateSort">
        update menu_detail
        set sort = null
        where MENU_ID = #{id}
    </update>

    <select id="selectContactMenuName" resultType="java.lang.String">
        SELECT CONCAT(parent.NAME, '-', child.NAME) AS full_name
        FROM menu_detail child
                 JOIN
             menu_detail parent
             ON
                 child.PARENT_ID = parent.MENU_ID
        where child.MENU_ID = #{menuId}
          and child.lang_type = #{langType}
          and parent.lang_type = #{langType}
    </select>

</mapper>
