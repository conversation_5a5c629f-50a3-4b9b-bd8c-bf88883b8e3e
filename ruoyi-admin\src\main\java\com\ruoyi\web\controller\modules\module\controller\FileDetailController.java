package com.ruoyi.web.controller.modules.module.controller;


import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.dto.FileDetailDto;
import com.ruoyi.system.entity.FileDetail;
import com.ruoyi.system.service.IFileDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 文件详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@RestController
@RequestMapping("/file-detail")
@Api(tags = "文件数据", description = "文件数据")
public class FileDetailController extends BaseController {

  @Autowired
  private IFileDetailService fileDetailService;

  @PostMapping("/uploadFile")
  @ApiOperation(value = "上传文件")
  @Anonymous
  public R<FileDetail> uploadFile(@RequestParam("file") final MultipartFile file) throws IOException {
    final FileDetail fileDetail = fileDetailService.uploadFile(file);
    return R.ok(fileDetail);
  }

  @GetMapping("/downloadFile")
  @ApiOperation(value = "下载")
  public void downloadFile(final HttpServletResponse response, final String fileId) throws IOException {
    if (fileId == null || fileId == "") {
      throw new RuntimeException("暂无可下载预案，请先生成预案");
    }

  }

  @DeleteMapping("/delete/{id}")
  @ApiOperation(value = "删除图片/轮播图")
  public R<Boolean> deleteFileDetailById(@PathVariable final String id) {
    return R.ok(fileDetailService.removeFileDetailById(id));
  }

  @PostMapping("/saveOrUpdateCarouselImage")
  @ApiOperation(value = "新增/修改轮播图")
  public R<Boolean> saveCarouselImage(@RequestBody final FileDetailDto fileDetail) {
    if (fileDetailService.saveOrUpdateCarouselImage(fileDetail)) {
      return R.ok();
    } else {
      return R.fail("操作失败");
    }
  }

  @GetMapping("/carouselImageList")
  @ApiOperation(value = "轮播图列表")
  public R<TableDataInfo> carouselImageList(final String fileName, final Integer isCarousel) {
    startPage();
    final List<FileDetailDto> fileDetails = fileDetailService.carouselImageList(fileName, isCarousel);
    List<FileDetailDto> fileDetailDtos = fileDetailService.carouselImageList(null, 1);
    final TableDataInfo dataTable = getDataTable(fileDetails);
    dataTable.setTotal(fileDetails.size());
    if (dataTable.getRows().size() >= 0) {
      return R.ok(dataTable);
    } else {
      return R.fail("操作失败");
    }
  }

  @GetMapping("/carouselImageDetail")
  @ApiOperation(value = "轮播图详情")
  public R<FileDetailDto> carouselImageDetail(final String id) {
    final FileDetailDto fileDetails = fileDetailService.carouselImageDetail(id);
    if (fileDetails != null) {
      return R.ok(fileDetails);
    } else {
      return R.fail("操作失败");
    }
  }
}

