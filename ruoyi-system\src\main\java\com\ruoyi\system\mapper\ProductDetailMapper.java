package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.system.domain.dto.HomeScreenDTO;
import com.ruoyi.system.entity.ProductDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品详情表  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Mapper
public interface ProductDetailMapper extends BaseMapper<ProductDetail> {

    List<ProductDetail> selectProductList(@Param("query") HomeScreenDTO homeScreenDTO);
}
