package com.ruoyi.system.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import com.ruoyi.system.domain.dto.FileDetailDto;
import com.ruoyi.system.entity.FileDetail;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 文件详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface IFileDetailService extends IService<FileDetail> {
  public Boolean saveOrUpdateFileDetail(FileDetail fileDetail);

  public Boolean removeFileDetailById(String id);

  public IPage<FileDetail> getFileDetailByPage(Integer currentPage, Integer pageSize, String fileDetailname);

  FileDetail uploadFile(MultipartFile file) throws IOException;

  boolean saveOrUpdateCarouselImage(FileDetailDto fileDetail);

  List<FileDetailDto> carouselImageList(String fileName, Integer isCarousel);

  FileDetailDto carouselImageDetail(String id);

  List<FileDetail> filesByProductId(String productId, Integer isCarousel);

  void updateFiles(String productId,List<FileDetailChilDto> files,int carousel);

}
