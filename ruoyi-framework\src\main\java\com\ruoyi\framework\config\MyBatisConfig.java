package com.ruoyi.framework.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.ruoyi.common.utils.StringUtils;
import org.apache.ibatis.io.VFS;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.util.ClassUtils;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * Mybatis支持*匹配扫描包
 *
 * <AUTHOR>
 */
@Configuration
public class MyBatisConfig {
  @Autowired
  private Environment env;

  static final String DEFAULT_RESOURCE_PATTERN = "**/*.class";

  public static String setTypeAliasesPackage(String typeAliasesPackage) {
    final ResourcePatternResolver resolver = (ResourcePatternResolver) new PathMatchingResourcePatternResolver();
    final MetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory(resolver);
    final List<String> allResult = new ArrayList<String>();
    try {
      for (String aliasesPackage : typeAliasesPackage.split(",")) {
        final List<String> result = new ArrayList<String>();
        aliasesPackage = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX
            + ClassUtils.convertClassNameToResourcePath(aliasesPackage.trim()) + "/" + DEFAULT_RESOURCE_PATTERN;
        final Resource[] resources = resolver.getResources(aliasesPackage);
        if (resources != null && resources.length > 0) {
          MetadataReader metadataReader = null;
          for (final Resource resource : resources) {
            if (resource.isReadable()) {
              metadataReader = metadataReaderFactory.getMetadataReader(resource);
              try {
                result.add(Class.forName(metadataReader.getClassMetadata().getClassName()).getPackage().getName());
              } catch (final ClassNotFoundException e) {
                e.printStackTrace();
              }
            }
          }
        }
        if (result.size() > 0) {
          final HashSet<String> hashResult = new HashSet<String>(result);
          allResult.addAll(hashResult);
        }
      }
      if (allResult.size() > 0) {
        typeAliasesPackage = String.join(",", (String[]) allResult.toArray(new String[0]));
      } else {
        throw new RuntimeException("mybatis typeAliasesPackage 路径扫描错误,参数typeAliasesPackage:" + typeAliasesPackage + "未找到任何包");
      }
    } catch (final IOException e) {
      e.printStackTrace();
    }
    return typeAliasesPackage;
  }

  public Resource[] resolveMapperLocations(final String[] mapperLocations) {
    final ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
    final List<Resource> resources = new ArrayList<Resource>();
    if (mapperLocations != null) {
      for (final String mapperLocation : mapperLocations) {
        try {
          final Resource[] mappers = resourceResolver.getResources(mapperLocation);
          resources.addAll(Arrays.asList(mappers));
        } catch (final IOException e) {
          // ignore
        }
      }
    }
    return resources.toArray(new Resource[resources.size()]);
  }

  @Bean
  public SqlSessionFactory sqlSessionFactory(final DataSource dataSource) throws Exception {
    String typeAliasesPackage = env.getProperty("mybatis.typeAliasesPackage");
    final String mapperLocations = env.getProperty("mybatis.mapperLocations");
    final String configLocation = env.getProperty("mybatis.configLocation");
    typeAliasesPackage = setTypeAliasesPackage(typeAliasesPackage);
    VFS.addImplClass(SpringBootVFS.class);

    final MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
    sessionFactory.setDataSource(dataSource);
    sessionFactory.setTypeAliasesPackage(typeAliasesPackage);
    sessionFactory.setMapperLocations(resolveMapperLocations(StringUtils.split(mapperLocations, ",")));
    sessionFactory.setConfigLocation(new DefaultResourceLoader().getResource(configLocation));
    return sessionFactory.getObject();
  }
}