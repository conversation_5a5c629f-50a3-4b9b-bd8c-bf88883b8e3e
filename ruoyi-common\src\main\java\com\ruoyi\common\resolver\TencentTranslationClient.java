package com.ruoyi.common.resolver;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: wang<PERSON><PERSON>
 * @create: 2024/8/30/0030
 * @Description:
 * @FileName: translate
 */

public class TencentTranslationClient {


  /**
   * 地域列表
   * 亚太东南（曼谷）	    ap-bangkok
   * 华北地区（北京）	    ap-beijing
   * 西南地区（成都）	    ap-chengdu
   * 西南地区（重庆）	    ap-chongqing
   * 华南地区（广州）	    ap-guangzhou
   * 港澳台地区（中国香港）	ap-hongkong
   * 亚太南部（孟买）	    ap-mumbai
   * 亚太东北（首尔）	    ap-seoul
   * 华东地区（上海）	    ap-shanghai
   * 华东地区（上海金融）	ap-shanghai-fsi
   * 华南地区（深圳金融）	ap-shenzhen-fsi
   * 亚太东南（新加坡）	    ap-singapore
   * 亚太东北（东京）	    ap-tokyo
   * 欧洲地区（法兰克福）	eu-frankfurt
   * 美国东部（弗吉尼亚）	na-ashburn
   * 美国西部（硅谷）	    na-siliconvalley
   * 北美地区（多伦多）	    na-toronto
   */

  @Value("${baidu-key.secretId}")
  private String secretId;
  @Value("${baidu-key.secretKey}")
  private String secretKey;


  private static final String[] SECRET_IDS = {
      "AKIDGNJ2SnxHPJ0TkojRwRjogKlyzmMirngM",
      "AKIDXt1vgJjvzyrplR4fFpOp9azVuAPUN71o",
      "AKIDIlFpaSVZhuGdwDlP9oUF1iOD3oV8gQ8x"

  };

  private static final String[] SECRET_KEYS = {
      "WotkV2MURDnBOQHvNdicKHAtRvDJFyWs",
      "fJqDNOErHj7Ohx2ucMuTu9C3h8MODB2i",
      "u2CVp7KcwEeME5266A81BX3o6PqKynWS"
  };


  private static final AtomicInteger index = new AtomicInteger(0);

  public TmtClient createClient() {
    final int idx = index.getAndUpdate(i -> (i + 1) % SECRET_IDS.length);
    final Credential cred = new Credential(SECRET_IDS[idx], SECRET_KEYS[idx]);
    final TmtClient client = new TmtClient(cred, "ap-tokyo");
    return client;
  }

  /**
   * text 需要翻译的文本
   * sourceLang 翻译文本的语种
   * targetLang 目标语种
   */
  public String translateText(final String text, final String sourceLang, final String targetLang) throws TencentCloudSDKException {
//    final TmtClient client = createClient();
//    final TextTranslateRequest req = new TextTranslateRequest();
//    req.setSourceText(text);
//    req.setSource(sourceLang);
//    req.setTarget(targetLang);
//    req.setProjectId(0L);
//    final TextTranslateResponse resp = client.TextTranslate(req);
    return AlibabaCloudTranslation.translate(text,sourceLang,targetLang);
//    return resp.getTargetText();
  }

  //
  public static void main(final String[] args) throws TencentCloudSDKException {

    final TencentTranslationClient tencentTranslationClient = new TencentTranslationClient();
    final String source = "成品描述";
    final String s = tencentTranslationClient.translateText(source, "zh", "ja");
    System.out.println(s);


  }
}