package com.ruoyi.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.StatusEnum;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.resolver.TencentTranslationClient;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.LabelDetail;
import com.ruoyi.system.entity.ProductWithLabel;
import com.ruoyi.system.mapper.LabelDetailMapper;
import com.ruoyi.system.mapper.MenuDetailMapper;
import com.ruoyi.system.mapper.ProductWithLabelMapper;
import com.ruoyi.system.service.ILabelDetailService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.common.utils.StringUtils.getUUID;


/**
 * <p>
 * 标签详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Service
public class LabelDetailServiceImpl extends ServiceImpl<LabelDetailMapper, LabelDetail> implements ILabelDetailService {
  @Value("${baidu-key.secretId}")
  private String secretId;
  @Value("${baidu-key.secretKey}")
  private String secretKey;
  @Autowired
  private LabelDetailMapper labelDetailMapper;
  @Autowired
  private ProductWithLabelMapper productWithLabelMapper;
  @Autowired
  private MenuDetailMapper menuDetailMapper;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean saveOrUpdateLabelDetail(final LabelDetail labelDetail) throws TencentCloudSDKException, InterruptedException {
    final boolean save;
    labelDetail.setLangType(LanguageEnum.CHINA.getValue());
    if (labelDetail == null || !labelDetail.getLangType().equals(LanguageEnum.CHINA.getValue())) {
      throw new ServiceException("数据有误", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final TencentTranslationClient tencentTranslationClient = new TencentTranslationClient();

    if (StringUtils.isNotBlank(labelDetail.getLabelId())) {
      labelDetail.setLabelId(getUUID());
      final LabelDetail tranEnslation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.EN.getValue(), labelDetail, tencentTranslationClient);
      final LabelDetail tranJaslation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.JA.getValue(), labelDetail, tencentTranslationClient);
      baseMapper.insert(labelDetail);
      baseMapper.insert(tranEnslation);
      save = baseMapper.insert(tranJaslation) > 0;
      return save;
    } else {
      update(labelDetail, new QueryWrapper<LabelDetail>().eq("label_id", labelDetail.getLabelId()).eq("lang_type", labelDetail.getLangType()));
      final LabelDetail tranEnslation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.EN.getValue(), labelDetail, tencentTranslationClient);
      final LabelDetail tranJaslation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.JA.getValue(), labelDetail, tencentTranslationClient);
      update(tranEnslation, new QueryWrapper<LabelDetail>().eq("label_id", labelDetail.getLabelId()).eq("lang_type", labelDetail.getLangType()));
      save = update(tranJaslation, new QueryWrapper<LabelDetail>().eq("label_id", labelDetail.getLabelId()).eq("lang_type", labelDetail.getLangType()));
      return save;
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)

  public Boolean removeLabelDetailById(final Integer id) {
    labelDetailMapper.delete(new QueryWrapper<LabelDetail>().eq("label_id", id));
    return productWithLabelMapper.delete(new QueryWrapper<ProductWithLabel>().eq("label_id", id)) > 0;
  }

  @Override
  public List<LabelDetail> getLabelDetailByPage(final Integer currentPage, final Integer pageSize, final String labelDetailname, final String langType) {
    if (langType == null || !langType.equals(LanguageEnum.CHINA.getValue())) {
      throw new ServiceException("数据有误", StatusEnum.SERVICE_ERROR.getResultCode());

    }
    final QueryWrapper<LabelDetail> queryWrapper = new QueryWrapper<>();
    queryWrapper.like(StringUtils.isNotBlank(labelDetailname), "label_name", labelDetailname)
        .eq("lang_type", LanguageEnum.CHINA.getValue())
        .orderByDesc("id");
    return labelDetailMapper.selectList(queryWrapper);
  }

  LabelDetail translation(final String sourceLang, final String targetLang, final LabelDetail labelSource, final TencentTranslationClient tencentTranslationClient) throws TencentCloudSDKException, InterruptedException {
    final LabelDetail labelTarget = new LabelDetail();
    BeanUtils.copyProperties(labelSource, labelTarget);
    labelTarget.setLabelName(StringUtils.isNotBlank(labelSource.getLabelName()) ? null : tencentTranslationClient.translateText(labelSource.getLabelName(), sourceLang, targetLang));
    labelTarget.setId(null);
    labelTarget.setLabelId(labelSource.getLabelId());
    labelTarget.setLangType(targetLang);
    return labelTarget;
  }
}
