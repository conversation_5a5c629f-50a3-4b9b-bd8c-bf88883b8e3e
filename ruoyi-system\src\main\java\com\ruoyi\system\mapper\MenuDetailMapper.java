package com.ruoyi.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.entity.MenuDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Mapper
public interface MenuDetailMapper extends BaseMapper<MenuDetail> {

  /**
   * @param menuId
   * @param kindName
   * @return
   */
  List<MenuDetail> selectChildrenByParentId(@Param("parentId") String menuId, @Param("langType") String langType);

  String selectMenuName(@Param("menuId") String menuId);

  void updateSort(@Param("id") String id);

  String selectContactMenuName(@Param("menuId") String menuId, @Param("langType") String langType);
}
