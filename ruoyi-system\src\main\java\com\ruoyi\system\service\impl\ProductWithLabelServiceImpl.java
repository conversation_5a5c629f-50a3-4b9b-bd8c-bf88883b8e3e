package com.ruoyi.system.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.entity.ProductWithLabel;
import com.ruoyi.system.mapper.ProductWithLabelMapper;
import com.ruoyi.system.service.IProductWithLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品与标签关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Service
public class ProductWithLabelServiceImpl extends ServiceImpl<ProductWithLabelMapper, ProductWithLabel> implements IProductWithLabelService {
  @Autowired
  private ProductWithLabelMapper productWithLabelMapper;


}
