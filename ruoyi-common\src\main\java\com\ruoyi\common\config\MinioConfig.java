/******************************************************************************
 *
 *
 *
 ******************************************************************************
 * 注意：本内容仅限于内部使用，禁止转发
 *****************************************************************************/
package com.ruoyi.common.config;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <ul>
 * <li>Title: 开发框架 MinioConfig</li>
 * <li>Description:  MinioConfig </li>
 * <li>Copyright: Copyright (c) 2021</li>
 * <li>Company: http://www.project.com</li>
 * </ul>
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/11
 */
@Configuration
@ConfigurationProperties(prefix = "minio")
@Data
public class MinioConfig {
  /**
   * 服务地址
   */
  private String url;


  /**
   * 用户名
   */
  private String accessKey;

  /**
   * 密码
   */
  private String secretKey;


  private String bucketName;


  @Bean
  public MinioClient getMinioClient() {
    return MinioClient.builder().endpoint(url).credentials(accessKey, secretKey).build();
  }
}
