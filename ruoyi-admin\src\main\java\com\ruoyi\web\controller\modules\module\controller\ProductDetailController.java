package com.ruoyi.web.controller.modules.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.system.domain.dto.TranslatedReqVO;
import com.ruoyi.system.domain.vo.ProductDetailVO;
import com.ruoyi.system.entity.ProductDetail;
import com.ruoyi.system.mapper.ProductDetailMapper;
import com.ruoyi.system.service.IProductDetailService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 商品详情表  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@RestController
@RequestMapping("/product-detail-zh")
@Api(tags = "商品详情", description = "商品详情")
public class ProductDetailController extends BaseController {

  @Autowired
  private IProductDetailService productDetailService;
  @Autowired
  private ProductDetailMapper productDetailMapper;

  @Anonymous
  @ApiOperation(value = "翻译(腾讯云)", notes = "翻译(腾讯云)")
  @GetMapping("/japanToChinese")
  public R<String> japanToChinese(@Validated final TranslatedReqVO req) {
    return R.ok(productDetailService.japanToChinese(req));
  }

  @PostMapping("/saveOrUpdate")
  @ApiOperation(value = "新增或者修改商品")
  public R saveProductDetail(@RequestBody final ProductDetail productDetail) throws TencentCloudSDKException, InterruptedException {
    if (productDetailService.saveOrUpdateProductDetail(productDetail)) {
      return R.ok();
    } else {
      return R.fail("操作失败");
    }
  }

  @DeleteMapping("/delete/{id}")
  @ApiOperation(value = "删除商品")
  public R<Boolean> deleteProductDetailById(@PathVariable final String id) {
    if (productDetailService.removeProductDetailById(id)) {
      return R.ok();
    } else {
      return R.fail("操作失败");

    }
  }

  @GetMapping("/findAllProductDetail")
  @ApiOperation(value = "商品详情")
  public R<ProductDetailVO> findProductDetail(final String productId, final String langType) {
    final ProductDetailVO one = productDetailService.findAllProductDetail(productId, langType);
    if (one != null) {
      return R.ok(one);
    } else {
      return R.fail("操作失败");
    }
  }

  //  商品名称、品相、颜色、材质、类别、状态
  @GetMapping("/page")
  @ApiOperation(value = "分页查询详情")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "lang", value = "语种", required = false, dataType = "String"),
      @ApiImplicitParam(name = "color", value = "颜色", required = false, dataType = "String"),
      @ApiImplicitParam(name = "appearance", value = "品相", required = false, dataType = "String"),
      @ApiImplicitParam(name = "lang", value = "材质", required = false, dataType = "String"),
      @ApiImplicitParam(name = "isShow", value = "状态", required = false, dataType = "Integer"),
      @ApiImplicitParam(name = "productName", value = "商品名称", required = false, dataType = "String")})
  public R<TableDataInfo> findProductDetailByPage(
      final String color,
      final String appearance,
      final String category,
      final String material,
      final Integer isShow,
      final String productName) {
    startPage();
    final List<ProductDetailVO> productDetailByPage = productDetailService.getProductDetailByPage(productName, color, appearance, material, category, isShow);
    final TableDataInfo dataTable = getDataTable(productDetailByPage);
    Long total = productDetailMapper.selectCount(new QueryWrapper<ProductDetail>().eq(StringUtils.isNotBlank(appearance), "appearance", appearance)
            .eq(StringUtils.isNotBlank(color), "color", color)
            .eq(StringUtils.isNotBlank(category), "category", category)
            .eq(StringUtils.isNotBlank(material), "material", material)
            .eq(isShow != null, "is_show", isShow)
            .eq("lang_type", LanguageEnum.CHINA.getValue())
            .orderByDesc("updated_time").orderByDesc("created_time"));
    dataTable.setTotal(total);
    if (productDetailByPage != null) {
      return R.ok(dataTable);
    } else {
      return R.fail("操作失败");
    }
  }


}

