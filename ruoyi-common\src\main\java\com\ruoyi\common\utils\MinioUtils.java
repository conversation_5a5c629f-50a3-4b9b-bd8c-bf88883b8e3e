package com.ruoyi.common.utils;

import com.ruoyi.common.config.MinioConfig;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;


@Component
public class MinioUtils {

  private static final Logger log = LoggerFactory.getLogger(MinioUtils.class);

  @Autowired
  private MinioClient minioClient;

  @Autowired
  private MinioConfig configuration;

  private final String NO_MATCH = "no-match" + "/" ;

  /**
   * @param name 名字
   * @return boolean
   * @Description description: 判断bucket是否存在，不存在则创建
   */
  public boolean existBucket(final String name) {
    boolean exists;
    try {
      exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(name).build());
      if (!exists) {
        minioClient.makeBucket(MakeBucketArgs.builder().bucket(name).build());
        exists = true;
      }
    } catch (final Exception e) {
      e.printStackTrace();
      exists = false;
    }
    return exists;
  }

  /**
   * @param bucketName 存储bucket名称
   * @return {@link Boolean }
   * @Description 创建存储bucket
   */
  public Boolean makeBucket(final String bucketName) {
    try {
      minioClient.makeBucket(MakeBucketArgs.builder()
          .bucket(bucketName)
          .build());
    } catch (final Exception e) {
      e.printStackTrace();
      return false;
    }
    return true;
  }

  /**
   * @param bucketName 存储bucket名称
   * @return {@link Boolean }
   * @Description 删除存储bucket
   */
  public Boolean removeBucket(final String bucketName) {
    try {
      minioClient.removeBucket(RemoveBucketArgs.builder()
          .bucket(bucketName)
          .build());
    } catch (final Exception e) {
      e.printStackTrace();
      return false;
    }
    return true;
  }


  /**
   * @param fileName 文件名称
   * @return {@link Boolean }
   * @Description 删除存储bucket
   */
  public void removeFile(final String fileName) {
    try {

      minioClient.removeObject(RemoveObjectArgs.builder()
          .bucket(configuration.getBucketName())
          .object(fileName)
          .build());

      System.out.println("Object removed successfully.");
    } catch (final Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * @param fileName 文件名称
   * @param dirName 一般为id
   * @Description 移动存储bucket
   */
  public String mvFile(String dirName,final String fileName) {
    try {
      String newPath = dirName+ "/" + fileName;
      if (!isExist(newPath)){
          // 创建 CopyObjectArgs 对象
          CopyObjectArgs copyObjectArgs = CopyObjectArgs.builder()
                  .bucket(configuration.getBucketName()) // 目标 bucket
                  .object(newPath) // 目标路径
                  .source(CopySource.builder().bucket(configuration.getBucketName()).object(NO_MATCH + fileName).build()) // 源 bucket 和路径
                  .build();

          // 复制对象
          minioClient.copyObject(copyObjectArgs);

          // 删除源文件
          removeFile(NO_MATCH + fileName);
      }

     return getFileUrl(newPath);

    } catch (MinioException e) {
      System.out.println("Error occurred: " + e);
    } catch (IOException e) {
        throw new RuntimeException(e);
    } catch (NoSuchAlgorithmException e) {
        throw new RuntimeException(e);
    } catch (InvalidKeyException e) {
        throw new RuntimeException(e);
    }
    return null;
  }

  private Boolean isExist(String objectName){
      try {
          StatObjectResponse statObjectResponse = minioClient.statObject(StatObjectArgs.builder()
                  .bucket(configuration.getBucketName()) // 目标 bucket
                  .object(objectName).build());
          if (statObjectResponse!=null){
            return true;
          }
      } catch (Exception e) {
          log.error("未识别到图片");
          return false;
      }
      return false;
  }


  /**
   * @param objectName 对象名称
   * @param method     方法
   * @param time       时间
   * @param timeUnit   时间单位
   * @return {@link String }
   * @Description 获取上传文件的url
   */
  public String getPolicyUrl(final String objectName, final Method method, final int time, final TimeUnit timeUnit, final String bucketName) {
    try {
      return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
          .method(method)
          .bucket(bucketName)
          .object(objectName)
          .expiry(time, timeUnit).build());
    } catch (final ErrorResponseException e) {
      e.printStackTrace();
    } catch (final InsufficientDataException e) {
      e.printStackTrace();
    } catch (final InternalException e) {
      e.printStackTrace();
    } catch (final InvalidKeyException e) {
      e.printStackTrace();
    } catch (final InvalidResponseException e) {
      e.printStackTrace();
    } catch (final IOException e) {
      e.printStackTrace();
    } catch (final NoSuchAlgorithmException e) {
      e.printStackTrace();
    } catch (final XmlParserException e) {
      e.printStackTrace();
    } catch (final ServerException e) {
      e.printStackTrace();
    }
    return null;
  }


  /**
   * @param file     文件
   * @param fileName 文件名称
   * @Description 上传文件
   */
  public String upload(final MultipartFile file, final String fileName) {
    // 使用putObject上传一个文件到存储桶中。
    try {
      final boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(configuration.getBucketName()).build());

      if (!bucketExists) {
        this.makeBucket(configuration.getBucketName());
      }
      final InputStream inputStream = file.getInputStream();
      minioClient.putObject(PutObjectArgs.builder()
          .bucket(configuration.getBucketName())
          .object(NO_MATCH + fileName)
          .stream(inputStream, file.getSize(), -1)
          .contentType(file.getContentType())
          .build());


      return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
          .method(Method.GET)
          .bucket(configuration.getBucketName())
          .object(NO_MATCH + fileName)
          .build()
      );
    } catch (final ErrorResponseException e) {
      e.printStackTrace();
    } catch (final InsufficientDataException e) {
      e.printStackTrace();
    } catch (final InternalException e) {
      e.printStackTrace();
    } catch (final InvalidKeyException e) {
      e.printStackTrace();
    } catch (final InvalidResponseException e) {
      e.printStackTrace();
    } catch (final IOException e) {
      e.printStackTrace();
    } catch (final NoSuchAlgorithmException e) {
      e.printStackTrace();
    } catch (final ServerException e) {
      e.printStackTrace();
    } catch (final XmlParserException e) {
      e.printStackTrace();
    }
    return null;
  }

//    /**
//     * @param file     文件
//     * @param fileName 文件名称
//     * @Description 上传文件
//     */
//    public void upload(MultipartFile file,String bucketName, String fileName) {
//        // 使用putObject上传一个文件到存储桶中。
//        try {
//            InputStream inputStream = file.getInputStream();
//            minioClient.putObject(PutObjectArgs.builder()
//                    .bucket(bucketName)
//                    .object(fileName)
//                    .stream(inputStream, file.getSize(), -1)
//                    .contentType(file.getContentType())
//                    .build());
//        } catch (ErrorResponseException e) {
//            e.printStackTrace();
//        } catch (InsufficientDataException e) {
//            e.printStackTrace();
//        } catch (InternalException e) {
//            e.printStackTrace();
//        } catch (InvalidKeyException e) {
//            e.printStackTrace();
//        } catch (InvalidResponseException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (ServerException e) {
//            e.printStackTrace();
//        } catch (XmlParserException e) {
//            e.printStackTrace();
//        }
//    }

  /**
   * @param objectName 对象名称
   * @param time       时间
   * @param timeUnit   时间单位
   * @return {@link String }
   * @Description 根据filename获取文件访问地址
   */
  public String getUrl(final String objectName, final String bucketName, final int time, final TimeUnit timeUnit) {
    String url = null;
    try {
      url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
          .method(Method.GET)
          .bucket(bucketName)
          .object(objectName)
          .expiry(time, timeUnit).build());
    } catch (final ErrorResponseException e) {
      e.printStackTrace();
    } catch (final InsufficientDataException e) {
      e.printStackTrace();
    } catch (final InternalException e) {
      e.printStackTrace();
    } catch (final InvalidKeyException e) {
      e.printStackTrace();
    } catch (final InvalidResponseException e) {
      e.printStackTrace();
    } catch (final IOException e) {
      e.printStackTrace();
    } catch (final NoSuchAlgorithmException e) {
      e.printStackTrace();
    } catch (final XmlParserException e) {
      e.printStackTrace();
    } catch (final ServerException e) {
      e.printStackTrace();
    }
    return url;
  }

  /**
   * @param fileName
   * @return {@link ResponseEntity }<{@link byte[] }>
   * @Description description: 下载文件
   */
  public ResponseEntity<byte[]> download(final String fileName, final String bucketName) {
    ResponseEntity<byte[]> responseEntity = null;
    InputStream in = null;
    ByteArrayOutputStream out = null;
    try {
      in = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build());
      out = new ByteArrayOutputStream();
      IOUtils.copy(in, out);
      //封装返回值
      final byte[] bytes = out.toByteArray();
      final HttpHeaders headers = new HttpHeaders();
      try {
        headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
      } catch (final UnsupportedEncodingException e) {
        e.printStackTrace();
      }
      headers.setContentLength(bytes.length);
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setAccessControlExposeHeaders(Arrays.asList("*"));
      responseEntity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
    } catch (final Exception e) {
      e.printStackTrace();
    } finally {
      try {
        if (in != null) {
          try {
            in.close();
          } catch (final IOException e) {
            e.printStackTrace();
          }
        }
        if (out != null) {
          out.close();
        }
      } catch (final IOException e) {
        e.printStackTrace();
      }
    }
    return responseEntity;
  }

  /**
   * @param objectFile 对象文件
   * @return {@link String }
   * @Description 根据文件名和桶获取文件路径
   */
  public String getFileUrl(final String objectFile) {
    try {

      return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
          .method(Method.GET)
          .bucket(configuration.getBucketName())
          .object(objectFile)
          .build()
      );
    } catch (final Exception e) {
      e.printStackTrace();
    }

    return null;
  }
}
