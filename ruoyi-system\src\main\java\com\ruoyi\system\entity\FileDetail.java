package com.ruoyi.system.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 文件详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@TableName("file_detail")
@ApiModel(value = "FileDetail对象", description = "文件详情")
public class FileDetail implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  @ApiModelProperty("商品id")
  @TableField("product_id")
  private String productId;

  @ApiModelProperty("文件名称")
  @TableField("file_name")
  private String fileName;

  @ApiModelProperty("文件类型")
  @TableField("file_type")
  private String fileType;

  @ApiModelProperty("文件大小")
  @TableField("file_size")
  private Double fileSize;

  @ApiModelProperty("文件路径")
  @TableField("file_path")
  private String filePath;

  @ApiModelProperty("排序")
  @TableField("file_sort")
  private Integer fileSort;

  @ApiModelProperty("上传时间")
  @TableField("created_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date createdTime;
  @ApiModelProperty("修改时间")
  @TableField("updated_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date updatedTime;
  @ApiModelProperty("图片跳转地址")
  @TableField("file_url")
  private String fileUrl;
  @ApiModelProperty("是否是轮播图 0-否 1-是")
  @TableField("is_carousel")
  private Integer isCarousel;
  @ApiModelProperty("描述")
  @TableField("descrition")
  private String descrition;
  @ApiModelProperty("其他地址")
  @TableField("external_link")
  private String externalLink;


}




