package com.ruoyi.common.utils;


import com.ruoyi.common.enums.StatusEnum;
import com.ruoyi.common.exception.ServiceException;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020-10-13
 */
public class FileUtils {

  /**
   * 获取文件名
   *
   * @param url url
   * @return 文件后缀
   */
  public static String fileNameFromUrl(final String url) {
    final String nonPramStr = url.substring(0, url.contains("?") ? url.indexOf("?") : url.length());
    return nonPramStr.substring(nonPramStr.lastIndexOf("-") + 1);
  }

  /**
   * 获取文件名
   *
   * @param path url
   * @return 文件后缀
   */
  public static String fileNameFromPath(final String path) {
    return path.substring(path.indexOf("-") + 1);
  }

  /**
   * 获取文件前缀
   *
   * @param fileName 文件名
   * @return 文件前缀
   */
  public static String prefixFromFileName(final String fileName) {
    return fileName.substring(0, fileName.lastIndexOf("."));
  }

  /**
   * 获取文件前缀
   *
   * @param fileName 文件名
   * @return 文件前缀
   */
  public static String pdfNameFromFileName(final String fileName) {
    return fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";
  }

  /**
   * 获取文件后缀名
   *
   * @param fileName 文件名
   * @return 文件后缀
   */
  public static String suffixNameFromFileName(final String fileName) {
    return fileName.substring(fileName.lastIndexOf(".") + 1);
  }

  /**
   * 获取文件后缀
   *
   * @param fileName 文件名
   * @return 文件后缀
   */
  public static String suffixFromFileName(final String fileName) {
    return fileName.substring(fileName.lastIndexOf("."));
  }

  /**
   * @param fileName 文件名
   * @return java.lang.String
   * <AUTHOR>
   * @date 2020-10-13
   * 生成随机的文件名
   */
  public static String generatorRandomFileName(final String fileName) {
    return LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + "/" + UUID.randomUUID().toString().replace("-", "") + "-" + fileName;
  }

  /**
   * @param fileName 文件名
   * @return java.lang.String
   * <AUTHOR>
   * @date 2020-10-13
   * 按日期存储生成文件名
   */
  public static String generatorFileName(final String fileName) {
    return LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + "/" + fileName;
  }

  /**
   * 生成随机的文件名
   *
   * @return java.lang.String
   * <AUTHOR>
   * @date 2020-10-13
   */
  public static String generatorRandomFileName() {
    return LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + "/" + UUID.randomUUID().toString().replace("-", "");
  }

  /**
   * 生成随机的文件名
   *
   * @return java.lang.String
   * <AUTHOR>
   * @date 2020-10-13
   */
  public static String generatorRandomImageName(final String imageType) {
    return LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + "/" + UUID.randomUUID().toString().replace("-", "") + "." + imageType;
  }

  /**
   * 校验待处理的文件类型是否支持
   *
   * <AUTHOR>
   * @date 2020-10-28
   */

  public static String stringUncode(String param) {
    if (param != null && !param.trim().equals("")) {
      try {
        //测试%转义
        param = param.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
        //转码
        param = URLEncoder.encode(param, "utf-8");
        //解码
        param = URLDecoder.decode(param, "UTF-8");
      } catch (final UnsupportedEncodingException e) {
        e.printStackTrace();
      }
    }
    return param;
  }

  public static void validateFileType(String fileType) {
    boolean include = false;
    if (fileType != null) {
      fileType = fileType.toLowerCase();
    }
    //遍历枚举类于文件类型匹配
//        String typeArray = ".txt,.mp4,.m4p,.mpg,.mpeg,.dat,.3gp,.mov,.rm,.ram,.rmvb,.wmv,.asf,.avi,.asx,.mkv.wav,.wma,.ra,.ogg,.mpc,.m4a,.aac,.mpa,.mp2,.m1a,.m2a,.mp3,.mid,.midi,.rmi,.mka,.ac3,.dts,.cda,.au,.snd,.aif,.aifc,.aiff,.zip,.rar,.ppt,.pptx,.wps";
    final String typeArray = ".png,.jpg,.jpeg,.webp";
    if (typeArray.contains(fileType)) {
      include = true;
    }
    //遍历枚举类都找不到该类型则提示不支持该文件类型
    if (!include) {
      final String s = "不支持" + fileType + "该文件类型";
      System.out.println(s);
      throw new ServiceException(s, StatusEnum.SERVICE_ERROR.getResultCode());

    }
  }
}
