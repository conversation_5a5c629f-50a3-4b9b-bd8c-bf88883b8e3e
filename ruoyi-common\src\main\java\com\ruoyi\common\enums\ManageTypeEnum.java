package com.ruoyi.common.enums;

/**
 * @author: wang<PERSON><PERSON>
 * @create: 2024/8/9/0009
 * @Description:
 * @FileName: ExceptionEnum
 */


import lombok.Getter;

public enum ManageTypeEnum {
  MATERIAL("材质管理", "material"),
  COLOR("颜色管理", "color"),
  CATEGORY("类别管理", "category"),
  APPEARANCE("品相管理", "appearance");

  /**
   * 描述
   */
  @Getter
  private final String desc;

  /**
   * 类型
   */
  @Getter
  private final String code;


  ManageTypeEnum(final String desc, final String code) {
    this.desc = desc;
    this.code = code;
  }


}
