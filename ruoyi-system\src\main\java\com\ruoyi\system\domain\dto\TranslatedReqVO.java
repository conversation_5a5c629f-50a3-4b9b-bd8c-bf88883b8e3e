package com.ruoyi.system.domain.dto;

/**
 * @author: wang<PERSON><PERSON>
 * @create: 2024/9/5/0005
 * @Description:
 * @FileName: TranslatedReqVO
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @project
 * @Classname TranslatedReqVO
 * @Description TODO
 * @Author: LJ<PERSON><PERSON>
 * @CreateTime: 2023-08-22  09:51
 */
@Data
public class TranslatedReqVO {

  @ApiModelProperty(value = "日语字符", required = true)
  @NotBlank(message = "翻译的字符不能为空！")
  private String str;

  @ApiModelProperty(value = "需要的翻译语言", required = true)
  @NotBlank(message = "需要的翻译语言不能为空！")
  private String sourceLang;

  @ApiModelProperty(value = "目标语言", required = true)
  @NotBlank(message = "目标语言不能为空！")
  private String targetLang;
}
