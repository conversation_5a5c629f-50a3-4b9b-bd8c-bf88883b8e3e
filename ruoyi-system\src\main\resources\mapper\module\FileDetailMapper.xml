<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FileDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.system.entity.FileDetail">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_type" property="fileType"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_path" property="filePath"/>
        <result column="file_sort" property="fileSort"/>
        <result column="created_time" property="createdTime"/>
        <result column="file_url" property="fileUrl"/>
    </resultMap>

    <select id="selectFileList" resultType="com.ruoyi.system.domain.dto.FileDetailChilDto">
        select file_url as url, id
        from file_detail
        where product_id = #{productId}
          and is_carousel = 0
        order by created_time asc
    </select>

    <select id="selectSuperFileList" resultType="com.ruoyi.system.domain.dto.FileDetailChilDto">
        select file_url as url, id
        from file_detail
        where product_id = #{productId}
          and is_carousel = 3
        order by created_time asc

    </select>

    <select id="selectMaxSort" resultType="java.lang.Integer">
        select max(file_sort)
        from file_detail
        where is_carousel in (1, 2)
    </select>
    <select id="selectSuperProductFileList" resultType="com.ruoyi.system.domain.dto.FileDetailChilDto">
        select file_url as url, id
        from file_detail
        where product_id = #{productId}
          and is_carousel = 4
        order by created_time asc
    </select>

    <update id="updateSort">
        update file_detail
        set file_sort = null
        where id = #{id}
    </update>

</mapper>
