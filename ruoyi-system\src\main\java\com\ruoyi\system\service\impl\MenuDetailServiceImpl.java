package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.ManageTypeEnum;
import com.ruoyi.common.enums.StatusEnum;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.resolver.TencentTranslationClient;
import com.ruoyi.common.utils.MinioUtils;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.FileDetail;
import com.ruoyi.system.entity.MenuDetail;
import com.ruoyi.system.entity.ProductDetail;
import com.ruoyi.system.entity.TypeManage;
import com.ruoyi.system.mapper.FileDetailMapper;
import com.ruoyi.system.mapper.MenuDetailMapper;
import com.ruoyi.system.mapper.ProductDetailMapper;
import com.ruoyi.system.mapper.TypeManageMapper;
import com.ruoyi.system.service.IFileDetailService;
import com.ruoyi.system.service.IMenuDetailService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.StringUtils.getUUID;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Service
public class MenuDetailServiceImpl extends ServiceImpl<MenuDetailMapper, MenuDetail> implements IMenuDetailService {
  @Autowired
  private MenuDetailMapper menuDetailMapper;
  @Autowired
  private IFileDetailService iFileDetailService;
  @Autowired
  TypeManageMapper typeManageMapper;
  @Autowired
  FileDetailMapper fileDetailMapper;
  @Autowired
  private MinioUtils minioUtils;
  @Autowired
  ProductDetailMapper productDetailMapper;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean saveMenuDetail(final MenuDetail menuDetail) throws TencentCloudSDKException, InterruptedException {
    boolean save = false;

    menuDetail.setLangType(LanguageEnum.CHINA.getValue());
    if (menuDetail == null || !menuDetail.getLangType().equals(LanguageEnum.CHINA.getValue())) {
      throw new ServiceException("数据有误", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final TencentTranslationClient tencentTranslationClient = new TencentTranslationClient();
    if (StringUtils.isBlank(menuDetail.getMenuId())) {
      //新增
      final MenuDetail nameBoolean = baseMapper.selectOne(new QueryWrapper<MenuDetail>()
          .eq("name", menuDetail.getName())
          .eq("ischild", menuDetail.getIschild())
          .eq("lang_type", LanguageEnum.CHINA.getValue()));
      if (nameBoolean != null) {
        throw new ServiceException("当前类别名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      }
      menuDetail.setMenuId(getUUID());
      menuDetail.setCreatedTime(new Date());
      menuDetail.setUpdatedTime(new Date());
      baseMapper.insert(menuDetail);
      final MenuDetail transEnlation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.EN.getValue(), menuDetail, tencentTranslationClient);
      final MenuDetail transJalation = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.JA.getValue(), menuDetail, tencentTranslationClient);
      baseMapper.insert(transEnlation);
      save = baseMapper.insert(transJalation) > 0;
    } else {
      //编辑

      menuDetail.setUpdatedTime(new Date());
      final List<MenuDetail> menuId = baseMapper.selectList(new QueryWrapper<MenuDetail>().eq("MENU_ID", menuDetail.getMenuId()));

      for (final MenuDetail menuDetail1 : menuId) {
        final String id = menuDetail1.getId();
        if (menuDetail1.getLangType().equals("zh")) {
          //判断-是不是修改自己的名称
          final MenuDetail nameBoolean = baseMapper.selectOne(new QueryWrapper<MenuDetail>()
              .eq("name", menuDetail.getName())
              .eq("ischild", menuDetail.getIschild())
              .eq("lang_type", LanguageEnum.CHINA.getValue()));
          if (nameBoolean != null && !nameBoolean.getMenuId().equals(menuDetail.getMenuId())) {
            throw new ServiceException("当前类别名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
          }
          BeanUtils.copyProperties(menuDetail, menuDetail1);
        } else {
          final MenuDetail transEnlation = translation(LanguageEnum.CHINA.getValue(), menuDetail1.getLangType(), menuDetail, tencentTranslationClient);
          BeanUtils.copyProperties(transEnlation, menuDetail1);
        }
        menuDetail1.setId(id);
        if (menuDetail.getSort() == null) {
          baseMapper.updateSort(menuDetail.getMenuId());
          menuDetail1.setSort(null);
        } else {
          menuDetail1.setSort(menuDetail.getSort());
        }
        if (menuDetail.getIschild().equals(2) && menuDetail1.getIschild().equals(1)) {
          menuDetail1.setParentId(null);
        }
        save = baseMapper.updateById(menuDetail1) > 0;
      }
    }
    if (menuDetail.getIschild().equals(1)
        && menuDetail.getFileUrls() != null
        && menuDetail.getFileUrls().size() > 0) {
      final List<FileDetail> collect = menuDetail.getFileUrls().stream().map(item -> {
        final FileDetail fileDetail = iFileDetailService.getById(item.getId());
        fileDetail.setProductId(menuDetail.getMenuId());
        fileDetail.setIsCarousel(0);
        fileDetail.setFileUrl(minioUtils.mvFile(menuDetail.getMenuId(), fileDetail.getFileName()));
        iFileDetailService.updateById(fileDetail);
        return fileDetail;
      }).collect(Collectors.toList());
    }
    // Todo 更新文件
    iFileDetailService.updateFiles(menuDetail.getMenuId(), menuDetail.getFileUrls(), 0);
    return save;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean deleteById(final String menuId) {
    final List<MenuDetail> menuId1 = baseMapper.selectList(new QueryWrapper<MenuDetail>().eq("PARENT_ID", menuId));
    if (!menuId1.isEmpty()) {
      throw new ServiceException("当前类别含有子类别，请先删除子类别", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final List<ProductDetail> appearance = productDetailMapper.selectList(new QueryWrapper<ProductDetail>().eq("category", menuId));
    if (!appearance.isEmpty()) {
      throw new ServiceException("当前有商品存在该类别，请先修改或删除商品", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    return baseMapper.delete(new QueryWrapper<MenuDetail>().eq("MENU_ID", menuId)) > 0;
  }

  @Override
  public List<MenuDetail> findMenuDetailByPage(final String menuId) {
    final QueryWrapper<MenuDetail> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .eq("lang_type", LanguageEnum.CHINA.getValue());
    queryWrapper
        .eq(StringUtils.isNotBlank(menuId), "MENU_ID", menuId)
        .or().eq("lang_type", LanguageEnum.CHINA.getValue())
        .eq(StringUtils.isNotBlank(menuId), "PARENT_ID", menuId)
        .orderByAsc("sort IS NULL")
        .orderByAsc(true, "sort")
        .orderByDesc("updated_time")
        .orderByDesc("created_time");
    final List<MenuDetail> menuDetails = baseMapper.selectList(queryWrapper);
    for (final MenuDetail menuDetail : menuDetails) {
      if (StringUtils.isNotBlank(menuDetail.getParentId())) {
        final String s = baseMapper.selectMenuName(menuDetail.getParentId());
        menuDetail.setParentName(s);
      }
    }
    return menuDetails;
  }

  @Override
  public MenuDetail getDetail(final String id) {
    final MenuDetail menuDetail = baseMapper.selectOne(new QueryWrapper<MenuDetail>().eq("MENU_ID", id).eq("lang_type", LanguageEnum.CHINA.getValue()));
    menuDetail.setFileUrls(fileDetailMapper.selectFileList(menuDetail.getMenuId()));
    return menuDetail;
  }

  public static List<TopMenuVO> convertToTopMenuVO(final List<TypeManage> typeManageList) {
    // 创建一个Map用于存储父级菜单的TopMenuVO对象
    final Map<String, TopMenuVO> menuMap = new HashMap<>();
    // 遍历TypeManage列表，创建父级和子级的映射关系
    for (final TypeManage typeManage : typeManageList) {
      final String menuId = typeManage.getMenuId();
      final String typeName = typeManage.getName();
      final String typeId = typeManage.getTypeId();
      final String menuName = typeManage.getMenuName();
      // 如果menuId为null，则不处理该条数据
      if (menuId == null) {
        continue;
      }
      // 如果父级菜单对象不存在，则创建
      TopMenuVO menuVO = menuMap.get(menuId);
      if (menuVO == null) {
        menuVO = new TopMenuVO();
        menuVO.setLabel(menuName); // parent label
        menuVO.setValue(menuId); // parent value
        menuVO.setChildren(new ArrayList<>());
        menuMap.put(menuId, menuVO);
      }
      // 创建子级菜单对象
      final TopMenuVO childVO = new TopMenuVO();
      childVO.setLabel(typeName); // child label
      childVO.setValue(typeId); // child value
      childVO.setChildren(new ArrayList<>()); // 子级不再有子级菜单

      // 将子级菜单添加到父级菜单的children列表中
      menuVO.getChildren().add(childVO);
    }

    // 将所有的父级菜单对象收集到一个列表中并返回
    return new ArrayList<>(menuMap.values());
  }

  @Override
  public List<TopMenuVO> getMenuList(final Integer menuType, final String codeType) {
    List<TopMenuVO> list = new ArrayList<>();
    if (menuType.equals(1) || menuType.equals(2)) {
      final List<MenuDetail> ischild = baseMapper.selectList(new QueryWrapper<MenuDetail>().eq("ISCHILD", menuType).eq(StringUtils.isNotBlank(codeType), "PARENT_ID", codeType).eq("lang_type", "zh").orderByAsc("sort IS NULL")
              .orderByAsc(true, "sort")
              .orderByDesc("updated_time")
              .orderByDesc("created_time"));
      list = ischild.stream().map(item -> {
        final TopMenuVO topMenuVO = new TopMenuVO();
        topMenuVO.setValue(item.getMenuId());
        topMenuVO.setLabel(item.getName());
        return topMenuVO;
      }).collect(Collectors.toList());
      return list;
    } else if (menuType.equals(3)) {
      final List<TypeManage> ischild = typeManageMapper.selectList(new QueryWrapper<TypeManage>().eq("manage_type", codeType).eq("lang_type", LanguageEnum.CHINA.getValue())
      );
      //分类管理 material-材质管理 color-颜色管理 appearance-品相管理
      if (codeType.equals(ManageTypeEnum.MATERIAL.getCode())) {
        return convertToTopMenuVO(ischild);
      } else if (codeType.equals(ManageTypeEnum.COLOR.getCode()) || codeType.equals(ManageTypeEnum.APPEARANCE.getCode())) {
        list = ischild.stream().map(item -> {
          final TopMenuVO topMenuVO = new TopMenuVO();
          topMenuVO.setValue(item.getMenuId());
          topMenuVO.setLabel(item.getMenuName());
          return topMenuVO;
        }).collect(Collectors.toList());
        return list;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  MenuDetail translation(final String sourceLang, final String targetLang, final MenuDetail menuSource, final TencentTranslationClient tencentTranslationClient) throws TencentCloudSDKException, InterruptedException {
    final MenuDetail menuTarget = new MenuDetail();
    BeanUtils.copyProperties(menuSource, menuTarget);
    menuTarget.setName(StringUtils.isBlank(menuSource.getName()) ? null : tencentTranslationClient.translateText(menuSource.getName(), sourceLang, targetLang));
    menuTarget.setId(null);
    menuTarget.setMenuId(menuSource.getMenuId());
    menuTarget.setLangType(targetLang);
    return menuTarget;
  }
}
