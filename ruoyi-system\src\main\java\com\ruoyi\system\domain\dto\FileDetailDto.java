package com.ruoyi.system.domain.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 文件详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@ApiModel(value = "FileDetail对象", description = "文件详情")
public class FileDetailDto implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;
  @ApiModelProperty("文件名称")
  @NotBlank(message = "文件名称不能为空")
  @Size(min = 0, max = 100, message = "文件名称长度不能超过100个字符")
  private String fileName;
  @ApiModelProperty("文件路径")
  private String filePath;
  @ApiModelProperty("排序")
  @NotBlank(message = "排序不能为空")
  @Size(min = 0, max = 100, message = "排序长度不能超过100个字符")
  private Integer fileSort;
  @ApiModelProperty("描述")
  @NotBlank(message = "描述不能为空")
  @Size(min = 0, max = 500, message = "描述长度不能超过500个字符")
  private String descrition;
  @ApiModelProperty("是否是轮播图 0-否 1-是 2-禁用")
  @NotBlank(message = "是否启用不能为空")
  private Integer isCarousel;
  @ApiModelProperty("上传时间")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date createdTime;
  private List<FileDetailChilDto> fileUrls;
  @ApiModelProperty("其他地址")
  @NotBlank(message = "其他地址用不能为空")
  @Size(min = 0, max = 500, message = "描述长度不能超过500个字符")
  private String externalLink;
}




