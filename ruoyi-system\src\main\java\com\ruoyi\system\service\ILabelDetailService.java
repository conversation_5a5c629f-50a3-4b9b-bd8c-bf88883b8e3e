package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.entity.LabelDetail;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

import java.util.List;

/**
 * <p>
 * 标签详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface ILabelDetailService extends IService<LabelDetail> {
  public Boolean saveOrUpdateLabelDetail(LabelDetail labelDetail) throws TencentCloudSDKException, InterruptedException;

  public Boolean removeLabelDetailById(Integer id);

  public List<LabelDetail> getLabelDetailByPage(Integer currentPage, Integer pageSize, String labelDetailname, String langType);
}
