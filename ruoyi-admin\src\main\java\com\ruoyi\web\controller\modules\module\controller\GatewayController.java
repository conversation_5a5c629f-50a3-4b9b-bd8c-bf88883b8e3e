package com.ruoyi.web.controller.modules.module.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import com.ruoyi.system.domain.dto.FileDetailDto;
import com.ruoyi.system.domain.dto.HomeScreenDTO;
import com.ruoyi.system.domain.vo.ProductDetailVO;
import com.ruoyi.system.entity.FileDetail;
import com.ruoyi.system.entity.MenuDetail;
import com.ruoyi.system.mapper.MenuDetailMapper;
import com.ruoyi.system.service.GateWayService;
import com.ruoyi.system.service.IFileDetailService;
import com.ruoyi.system.service.IProductDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/gate-way")
@Api(tags = "门户页面", description = "门户页面接口")
public class GatewayController extends BaseController {

    @Autowired
    private GateWayService gateWayService;

    @Autowired
    private IProductDetailService productDetailService;

    @Autowired
    private IFileDetailService fileDetailService;
    @Autowired
    private MenuDetailMapper menuDetailMapper;

    /**
     * 头部菜单下拉
     *
     * @return
     */
    @Anonymous
    @ApiOperation(value = "头部")
    @GetMapping("/product/list-top")
    public R<List<Map<String, Object>>> productListTop(final HttpServletRequest request) {
        final String language = request.getHeader("Language");
        return gateWayService.productListTop(language);
    }

    /**
     * 筛选内容
     *
     * @return
     */
    @Anonymous
    @GetMapping("/product/screen-list")
    @ApiOperation(value = "筛选内容")
    public R<Map<String, Object>> screenList(@RequestParam(value = "menuId", required = false) final String menuId, final HttpServletRequest request) {
        final String lang = request.getHeader("Language");
        return gateWayService.screenList(lang, menuId);
    }

    /**
     * 获取所有产品
     *
     * @return
     */
    @Anonymous
    @GetMapping("/product/List")
    @ApiOperation(value = "商品")
    public R<TableDataInfo> productList(final HomeScreenDTO homeScreenDTO, final HttpServletRequest request) {
        startPage();
        homeScreenDTO.setLang(request.getHeader("Language"));
        return R.ok(getDataTable(gateWayService.productList(homeScreenDTO)));
    }

    @Anonymous
    @GetMapping("/product/new-single")
    @ApiOperation(value = "新进商品")
    public R<TableDataInfo> newSingle(final HttpServletRequest request) {
        startPage();
        final HomeScreenDTO homeScreenDTO = new HomeScreenDTO();
        homeScreenDTO.setSortType("last");
        homeScreenDTO.setLang(request.getHeader("Language"));
        return R.ok(getDataTable(gateWayService.productList(homeScreenDTO)));
    }


    @GetMapping("/findProductDetail")
    @Anonymous
    @ApiOperation(value = "商品详情")
    public R<ProductDetailVO> findProductDetail(@RequestParam("productId") final String productId, final HttpServletRequest request) {
        final String language = request.getHeader("Language");
        final ProductDetailVO productTypeDetail = productDetailService.getProductTypeDetail(productId, language);
        if (language.equals(LanguageEnum.CHINA.getValue())){
            productTypeDetail.setPrice(productTypeDetail.getChinaPrice());
        } else  if (language.equals(LanguageEnum.JA.getValue())){
            productTypeDetail.setPrice(productTypeDetail.getJapPrice());
        } else  if (language.equals(LanguageEnum.EN.getValue())){
            productTypeDetail.setPrice(productTypeDetail.getEngPrice());
        }
        //同意返回日文金额
        productTypeDetail.setPrice(productTypeDetail.getJapPrice());
        //图片详情
        final List<FileDetail> fileDetails = fileDetailService.filesByProductId(productId, 0);
        List<FileDetailChilDto> fileDetailChilDtos = new ArrayList<>();
        for (FileDetail fileDetail : fileDetails) {
            FileDetailChilDto fileDetailChilDto = new FileDetailChilDto();
            fileDetailChilDto.setId(fileDetail.getId());
            fileDetailChilDto.setUrl(fileDetail.getFileUrl());
            fileDetailChilDtos.add(fileDetailChilDto);
        }
        productTypeDetail.setFileUrls(fileDetailChilDtos);
        return R.ok(productTypeDetail);
    }

    @GetMapping("/banner/list")
    @Anonymous
    @ApiOperation(value = "banner图")
    public R<List<?>> bannerList() {
        final List<FileDetailDto> fileDetails = fileDetailService.carouselImageList(null, 1);
        final TableDataInfo dataTable = getDataTable(fileDetails);
        return R.ok(dataTable.getRows());
    }

    @GetMapping("/cover/picture")
    @Anonymous
    @ApiOperation(value = "cover图")
    public R<String> coverPicture(@RequestParam(value = "menuId", required = false) String menuId, final HttpServletRequest request) {
        if (menuId == null || menuId.isEmpty()) {
            QueryWrapper<MenuDetail> menuDetailQueryWrapper = new QueryWrapper<>();
            menuDetailQueryWrapper.isNull("parent_id");
            menuDetailQueryWrapper.eq("lang_type", request.getHeader("Language"));
            List<MenuDetail> menuDetails = menuDetailMapper.selectList(menuDetailQueryWrapper);
            Random random = new Random();
            int randomIndex = random.nextInt(menuDetails.size());
            menuId = menuDetails.get(randomIndex).getMenuId();
        }
        List<FileDetail> fileDetails = fileDetailService.filesByProductId(menuId, 0);
        if (fileDetails.isEmpty()) {
            return R.ok("");
        }
        return R.ok(fileDetails.get(0).getFileUrl());
    }

}
