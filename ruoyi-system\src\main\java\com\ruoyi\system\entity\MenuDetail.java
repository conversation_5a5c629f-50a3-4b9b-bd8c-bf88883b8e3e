package com.ruoyi.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@TableName("menu_detail")
@ApiModel(value = "MenuDetailZh对象", description = "")
public class MenuDetail implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableField("MENU_ID")
  private String menuId;

  @TableField("PARENT_ID")
  private String parentId;

  @TableField("NAME")
  @ApiModelProperty("名称")
  @NotBlank(message = "名称不能为空")
  @Size(min = 0, max = 100, message = "名称长度不能超过100个字符")
  private String name;

  @ApiModelProperty("描述")
  @TableField("descrition")
  private String descrition;

  @TableField("sort")
  @ApiModelProperty("排序")
  private Integer sort;

  @TableField("ischild")
  @ApiModelProperty("层级")
  @NotBlank(message = "层级不能为空")
  private Integer ischild;
  @TableId(type = IdType.ASSIGN_UUID)
  private String id;
  @ApiModelProperty("语种")
  @TableField("lang_type")
  private String langType;
  @ApiModelProperty("创建时间")
  @TableField("created_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date createdTime;
  @ApiModelProperty("修改时间")
  @TableField("updated_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date updatedTime;
  @ApiModelProperty("管理种类")
  @NotBlank(message = "管理种类不能为空")
  @TableField("manage_type")
  private String manageType;
  @ApiModelProperty("语种")
  @TableField(exist = false)
  private List<FileDetailChilDto> fileUrls;
  @TableField(exist = false)
  private String parentName;

}




