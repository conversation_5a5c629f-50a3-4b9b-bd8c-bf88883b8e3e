<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProductDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.system.entity.ProductDetail">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="appearance" property="appearance"/>
        <result column="product_no" property="productNo"/>
        <result column="color" property="color"/>
        <result column="material" property="material"/>
        <result column="metal_parts" property="metalParts"/>
        <result column="engraving" property="engraving"/>
        <result column="product_size" property="productSize"/>
        <result column="accessories" property="accessories"/>
        <result column="product_style" property="productStyle"/>
        <result column="category" property="category"/>
        <result column="another_name" property="anotherName"/>
        <result column="description" property="description"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_user" property="createdUser"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="updated_user" property="updatedUser"/>
        <result column="is_delete" property="isDelete"/>
        <result column="china_price" property="chinaPrice"/>
        <result column="eng_price" property="engPrice"/>
        <result column="jap_price" property="japPrice"/>
    </resultMap>

    <select id="selectProducePage" resultType="com.ruoyi.system.entity.ProductDetail">
        select *
        from ${productTable} ${ew.customSqlSegment}
    </select>


    <select id="selectProductList" resultType="com.ruoyi.system.entity.ProductDetail">
        select
            pd.*
        from
            product_detail pd
        <if test="query.labelId !=null and query.labelId!=''">
            inner join product_with_label pwl
                on pd.product_id = pwl.product_id
        </if>
        where
            pd.lang_type = #{query.lang} and pd.is_show = 1
        <if test="query.labelId !=null and query.labelId!=''">
            and pwl.label_id = #{query.labelId}
        </if>
        <if test="query.productName !=null and query.productName!=''">
            and (( pd.name like CONCAT('%', #{query.productName}, '%'))
                or
            (pd.another_name like CONCAT('%', #{query.productName}, '%'))
            )
        </if>
        <if test="query.menuId !=null and query.menuId !=null">
            and   pd.menu_id = #{query.menuId}
        </if>
        <if test="query.appearanceList !=null and query.appearanceList.size>0">
            and   pd.appearance in
            <foreach collection="query.appearanceList" item="detail" open="(" close=")" separator=",">
                #{detail}
            </foreach>
        </if>
        <if test="query.materialList !=null and query.materialList.size>0">
            and   pd.material in
            <foreach collection="query.materialList" item="detail" open="(" close=")" separator=",">
                #{detail}
            </foreach>
        </if>
        <if test="query.kindList !=null and query.kindList.size>0">
            and   pd.category in
            <foreach collection="query.kindList" item="detail" open="(" close=")" separator=",">
                #{detail}
            </foreach>
        </if>
        <if test="query.colorList !=null and query.colorList.size>0">
            and   pd.color in
            <foreach collection="query.colorList" item="detail" open="(" close=")" separator=",">
                #{detail}
            </foreach>
        </if>
        <if test="query.sortType !=null and query.sortType != '' ">
            <if test="query.sortType == 'last'">
                order by pd.created_time desc
            </if>
            <if test="query.sortType == 'desc'">
                order by pd.china_price desc
            </if>
            <if test="query.sortType == 'asc'">
                order by pd.china_price asc
            </if>
        </if>

    </select>
</mapper>
