package com.ruoyi.common.resolver;


import com.alibaba.fastjson2.JSON;
import com.aliyun.alimt20181012.models.TranslateResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 阿里云翻译包
 * <dependency>
 * <groupId>com.aliyun</groupId>
 * <artifactId>alimt20181012</artifactId>
 * <version>1.1.0</version>
 * </dependency>
 */
public class AlibabaCloudTranslation {

    private static final Logger log = LoggerFactory.getLogger(AlibabaCloudTranslation.class);

    //默认id
//    public static final String DEFAULT_ACCESS_KEY_ID = "LTAI5tKmjSv9PziFmKb79WjD";
//    public static final String DEFAULT_ACCESS_KEY_ID = "LTAI5tNam6veheaqueDYiQty";
    public static final String DEFAULT_ACCESS_KEY_ID = "LTAI5tJQGHLEWpozmnzRhNNd";

    //默认secret
//    public static final String DEFAULT_ACCESS_KEY_SECRET = "******************************";
//    public static final String DEFAULT_ACCESS_KEY_SECRET = "******************************";
    public static final String DEFAULT_ACCESS_KEY_SECRET = "******************************";

    private static final String[] SECRET_IDS = {
            "LTAI5tKmjSv9PziFmKb79WjD",
            "LTAI5tNam6veheaqueDYiQty",
            "LTAI5tJQGHLEWpozmnzRhNNd"

    };

    private static final String[] SECRET_KEYS = {
            "******************************",
            "******************************",
            "******************************"
    };

    //中文
    public static final String ZH = "zh";
    public static final String JA = "ja";

    //英文
    public static final String EN = "en";

    //设置格式类型
    public static final String TEXT = "text";

    // 访问的域名
    public static final String MT_ALIYUNCS_COM = "mt.aliyuncs.com";

    //设置场景为一般

    public static final String EXCEPTION_MSG = "翻译失败：[阿里云的id或密码错误][网络异常]";
    public static final char CHARACTER_UNDERLINE = '_';
    public static final char NULL_CHARACTER = ' ';

    private static final AtomicInteger index = new AtomicInteger(0);


    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId     阿里云密钥id
     * @param accessKeySecret 阿里云密钥密码
     * @return Client 客户端
     * @throws Exception 初始化客户端异常
     */
    public static com.aliyun.alimt20181012.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = MT_ALIYUNCS_COM;
        return new com.aliyun.alimt20181012.Client(config);
    }

    public void main(String[] args) {
        System.out.println(translate("アクセサリー","ja","zh"));
    }


    /**
     * 获取翻译结果  如果密钥和密码为空，则默认使用这个特定的id与密码，但有可能失效
     *
     * @param text            翻译内容
     * @return String 翻译结果
     */
    public static String translate(String text,String sourceLangType,String targetLangType) {

        if (StringUtils.isEmpty(text)) {
            return "";
        }

        // 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性,更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
        com.aliyun.alimt20181012.Client client;
        try {
            final int idx = index.getAndUpdate(i -> (i + 1) % SECRET_IDS.length);
                client = AlibabaCloudTranslation.createClient(SECRET_IDS[idx], SECRET_KEYS[idx]);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

        com.aliyun.alimt20181012.models.TranslateRequest translateRequest = new com.aliyun.alimt20181012.models.TranslateRequest()
                .setFormatType(TEXT)
                .setSourceLanguage(sourceLangType)
                .setTargetLanguage(targetLangType)
                .setSourceText(text)
                .setScene("communication");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        TranslateResponse translateResponse;
        try {
            // 获取响应
            translateResponse = client.translateWithOptions(translateRequest, runtime);
            log.info("原文：{}; 翻译接口返回数据：{}", text, JSON.toJSONString(translateResponse));
        } catch (Exception error) {

          throw new RuntimeException(EXCEPTION_MSG);
        }
        System.out.println("原文："+ text + " --> 翻译结果："+translateResponse.getBody().getData().getTranslated());
        return translateResponse.getBody().getData().getTranslated();
    }

//    /**
//     * 请求一次接口获取所有文本的翻译，通过map映射  map<文本,翻译结果> 并返回
//     *
//     * @param textList        翻译内容集合
//     * @return LinkedHashMap<文本, 翻译结果>
//     */
//    public static Map<String, String> getResultsMap(List<String> textList) {
//        //读取文件 没有会创建
//        String filePath = "C:\\translateAFile.txt";
//        Map<String, String> resultsMap = readLocalFile(filePath);
//        for (String key : textList) {
//            String value = resultsMap.get(key);
//            if (value == null) {
//                value = getResults(key);
//                //去除特殊字符串
//                value = removeSpecialCharacters(value);
//                resultsMap.put(key, value);
//            }
//        }
//        StringBuilder sb = new StringBuilder();
//        resultsMap.forEach((x, y) -> {
//            sb.append(x).append(SEMICOLON).append(y).append("\n");
//        });
//        try (BufferedWriter bw = new BufferedWriter(new FileWriter(filePath))) {
//            // 将StringBuilder的内容写入文件
//            bw.write(sb.toString());
//            bw.newLine();
//        } catch (IOException e) {
//            System.err.println("Error writing file: " + e.getMessage());
//        }
//        return resultsMap;
//    }


    /**
     * 去除字符串的特殊字符串
     *
     * @param text 字符串
     * @return String 字符串
     */
    public static String removeSpecialCharacters(String text) {
        return text.replaceAll("[\n`~!,#$@%^&*()+=|{}':;\\[\\].<>/?！￥…（）—【】‘；：”“’。，、？]", "");
    }

    /**
     * 翻译并改为大写 空格变为下划线  to upper case with underscore ->  TO_UPPER_CASE_WITH_UNDERSCORE 格式
     *
     * @param text 字符串
     * @return String 字符串
     */
    public static String toUpperCaseWithUnderscore(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == NULL_CHARACTER) {
                result.append(CHARACTER_UNDERLINE);
            } else {
                result.append(Character.toUpperCase(c));
            }
        }
        return result.toString();
    }

    /**
     * 字符串改为 大驼峰格式  to camel case -> ToCamelCase
     *
     * @param text 字符串
     * @return String 字符串
     */
    public static String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        StringBuilder result = new StringBuilder();
        return getString(text, result);
    }

    public static String getString(String text, StringBuilder result) {
        boolean capitalizeNext = true;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == NULL_CHARACTER || c == CHARACTER_UNDERLINE) {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 下划线转大驼峰 TEST_ENUM -> TestEnum
     *
     * @param text 字符串
     * @return String 字符串
     */
    public static String toUnderCase(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        text = text.toLowerCase();
        StringBuilder result = new StringBuilder();
        return getString(text, result);
    }

    /**
     * 将驼峰式命名的字符串转换为下划线大写方式。如果转换前的驼峰式命名的字符串为空，则返回空字符串。
     * 例如：HelloWorld Hello-World -> HELLO_WORLD
     *
     * @param text 转换前的驼峰式命名的字符串
     * @return 转换后下划线大写方式命名的字符串
     */
    public static String underscoreName(String text) {
        StringBuilder result = new StringBuilder();
        if (text != null && !text.isEmpty()) {
            //是否存在特殊字符
            Pattern compile = Pattern.compile(".*[ _\\-`~!@#$%^&*()+=|{}':;,\\[\\].<>/?！￥…（）—【】‘；：”“’。，、？\\n\\r\\t].*");
            Matcher matcher = compile.matcher(text);
            if (matcher.matches()) {
                // 将所有字符转换为小写 将所有的非字母字符替换为空格 将所有连续的空格替换为一个空格 将所有空格替换为下划线 将所有字符转换为大写
                text = text.toLowerCase().replaceAll("[^a-z]", " ").replaceAll("\\s+", " ").replaceAll(" ", "_").toUpperCase();
                return text;
            }
            // 将第一个字符处理成大写
            result.append(text.substring(0, 1).toUpperCase());
            // 循环处理其余字符
            for (int i = 1; i < text.length(); i++) {
                String s = text.substring(i, i + 1);
                // 在大写字母前添加下划线
                if (s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                    result.append("_");
                }
                // 其他字符直接转成大写
                result.append(s.toUpperCase());
            }
        }
        return result.toString();
    }
}

