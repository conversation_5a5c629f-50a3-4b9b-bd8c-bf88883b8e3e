package com.ruoyi.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.StatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.FileUtils;
import com.ruoyi.common.utils.MinioUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import com.ruoyi.system.domain.dto.FileDetailDto;
import com.ruoyi.system.entity.FileDetail;
import com.ruoyi.system.mapper.FileDetailMapper;
import com.ruoyi.system.service.IFileDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 文件详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Service
public class FileDetailServiceImpl extends ServiceImpl<FileDetailMapper, FileDetail> implements IFileDetailService {
  @Autowired
  private FileDetailMapper fileDetailMapper;
  @Value("${web.upload-path}")
  private String uploadFilePath;
  @Autowired
  private MinioUtils minioUtils;

  @Override
  public Boolean saveOrUpdateFileDetail(final FileDetail fileDetail) {
    if (fileDetail.getId() == null) {
      return save(fileDetail);
    } else {
      return updateById(fileDetail);
    }
  }

  @Override
  public Boolean removeFileDetailById(final String id) {
    final FileDetail fileDetail = fileDetailMapper.selectById(id);
    minioUtils.removeFile(fileDetail.getFileName());
    return fileDetailMapper.deleteById(id) > 0;
  }

  @Override
  public IPage<FileDetail> getFileDetailByPage(final Integer currentPage, final Integer pageSize, final String fileDetailname) {
    final IPage<FileDetail> page = new Page<>(currentPage, pageSize);
    if (fileDetailname == "") {
      return fileDetailMapper.selectPage(page, null);
    }
    final QueryWrapper<FileDetail> queryWrapper = new QueryWrapper<>();
    queryWrapper.like("file_name", fileDetailname);
    queryWrapper.orderByDesc("id");
    return fileDetailMapper.selectPage(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public FileDetail uploadFile(final MultipartFile file) throws IOException {
    final String fileName =UUID.randomUUID().toString().replace("-", "").toLowerCase()+"-"+ file.getOriginalFilename();
    final String newCompanyImagePath = uploadFilePath + System.currentTimeMillis() + File.separator;
    final File imagePath = new File(newCompanyImagePath);
    if (!imagePath.exists()) {
      imagePath.mkdirs();
    }
    final File newFile = new File(newCompanyImagePath + fileName);
    if (!newFile.exists()) {
      newFile.createNewFile();
    }

    //获取文件类型
    String fileType = "";
    if (StringUtils.isNotBlank(fileName)) {
      final String[] fileNameArr = fileName.split("\\.");
      // 获取文件的后缀名
      final String suffix = fileNameArr[fileNameArr.length - 1];
      FileUtils.validateFileType(suffix);
      if (suffix.equalsIgnoreCase("png") ||
          suffix.equalsIgnoreCase("jpg") ||
          suffix.equalsIgnoreCase("jpeg")) {
        fileType = "img";
      }
    }
    //minio上传文件
    final String uploadFilePath = minioUtils.upload(file, fileName);
    final FileDetail warnPlanFile = new FileDetail();
    warnPlanFile.setFileName(fileName);
    warnPlanFile.setFileType(fileType);
    warnPlanFile.setFileSize(Double.valueOf(file.getSize()));
    warnPlanFile.setFilePath(newCompanyImagePath + fileName);
    warnPlanFile.setFileUrl(uploadFilePath);
    warnPlanFile.setCreatedTime(new Date());
    baseMapper.insert(warnPlanFile);
    final BufferedOutputStream out = new BufferedOutputStream(new FileOutputStream(newFile));
    out.write(file.getBytes());
    out.flush();
    out.close();

    return warnPlanFile;
  }

  @Override
  public boolean saveOrUpdateCarouselImage(final FileDetailDto fileDetail) {
    final QueryWrapper<FileDetail> fileDetailQueryWrapper = new QueryWrapper<>();
    fileDetailQueryWrapper.in("is_carousel", 1, 2)
        .eq("file_name", fileDetail.getFileName());
    final FileDetail fileDetail2 = baseMapper.selectOne(fileDetailQueryWrapper);
    //因为只有修改 不能修改成一样的 新增的时候没有传id 编辑有传
    if (fileDetail.getId() == null) {
      if (fileDetail2 != null) {
        throw new ServiceException("当前轮播图已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      }
    } else {
      if (fileDetail2 != null && !fileDetail2.getId().equals(fileDetail.getId())) {
        throw new ServiceException("当前轮播图已存在", StatusEnum.SERVICE_ERROR.getResultCode());
      }
    }
    if (fileDetail2 != null && !fileDetail2.getId().equals(fileDetail.getFileUrls().get(0).getId())) {
      //删除原来的文件
      baseMapper.deleteById(fileDetail.getId());
    }
    final List<FileDetailChilDto> fileUrls = fileDetail.getFileUrls();
    for (final FileDetailChilDto fileDetailChilDto : fileUrls) {
      final FileDetail fileDetail1 = baseMapper.selectById(fileDetailChilDto.getId());
      fileDetail1.setFileName(fileDetail.getFileName());
      fileDetail1.setFileUrl(fileDetailChilDto.getUrl());
      fileDetail1.setIsCarousel(fileDetail.getIsCarousel());
      fileDetail1.setDescrition(fileDetail.getDescrition());
      fileDetail1.setExternalLink(fileDetail.getExternalLink());
      if (fileDetail.getFileSort() == null) {
        baseMapper.updateSort(fileDetail.getId());
        fileDetail1.setFileSort(null);
      } else {
        fileDetail1.setFileSort(fileDetail.getFileSort());
      }
      if (fileDetail1.getCreatedTime() == null) {
        fileDetail1.setCreatedTime(new Date());
      }
      fileDetail1.setUpdatedTime(new Date());
      baseMapper.updateById(fileDetail1);
    }
    return true;
  }

  @Override
  public List<FileDetailDto> carouselImageList(final String fileName, final Integer isCarousel) {
    final QueryWrapper<FileDetail> fileDetailQueryWrapper = new QueryWrapper<>();
    if (isCarousel != null) {
      fileDetailQueryWrapper.eq("is_carousel", isCarousel);
    } else {
      fileDetailQueryWrapper.in("is_carousel", 1, 2);
    }
    fileDetailQueryWrapper.like(StringUtils.isNotBlank(fileName), "file_name", fileName)
        .orderByAsc("file_sort IS NULL")
        .orderByAsc("file_sort")
        .orderByDesc("updated_time").orderByDesc("created_time");
    final List<FileDetail> fileDetails = baseMapper.selectList(fileDetailQueryWrapper);
    final List<FileDetailDto> collect = fileDetails.stream().map(item -> {
      final FileDetailChilDto fileDetailChilDto = new FileDetailChilDto();
      final List<FileDetailChilDto> list = new ArrayList<>();
      list.add(fileDetailChilDto);
      fileDetailChilDto.setId(item.getId());
      fileDetailChilDto.setUrl(item.getFileUrl());
      final FileDetailDto fileDetailDto = new FileDetailDto();
      BeanUtils.copyProperties(item, fileDetailDto);
      fileDetailDto.setFileUrls(list);
      return fileDetailDto;
    }).collect(Collectors.toList());
    return collect;
  }

  @Override
  public FileDetailDto carouselImageDetail(final String id) {
    final List<FileDetailChilDto> fileUrls = new ArrayList<>();
    final FileDetailChilDto fileDetailChilDto = new FileDetailChilDto();
    final FileDetailDto fileDetailDto = new FileDetailDto();
    final FileDetail fileDetail = baseMapper.selectById(id);
    BeanUtils.copyProperties(fileDetail, fileDetailDto);
    fileDetailChilDto.setId(fileDetail.getId());
    fileDetailChilDto.setUrl(fileDetail.getFileUrl());
    fileUrls.add(fileDetailChilDto);
    fileDetailDto.setFileUrls(fileUrls);
    return fileDetailDto;
  }

  @Override
  public List<FileDetail> filesByProductId(final String productId, final Integer isCarousel) {
    final QueryWrapper<FileDetail> fileDetailQueryWrapper = new QueryWrapper<>();

    fileDetailQueryWrapper.eq("product_id", productId);
//    if (isCarousel == 3) {
      fileDetailQueryWrapper.eq("is_carousel", isCarousel);
//    }
    fileDetailQueryWrapper.orderByAsc("created_time");
    return fileDetailMapper.selectList(fileDetailQueryWrapper);
  }

  @Override
  public void updateFiles(final String productId, final List<FileDetailChilDto> files, final int carousel) {
    //获取该表原字段
    final List<FileDetail> dbFiles = fileDetailMapper.selectList(new QueryWrapper<FileDetail>().eq("product_id", productId).eq("is_carousel", carousel));
    final List<String> dbIds = dbFiles.stream().map(FileDetail::getId).collect(Collectors.toList());
    final List<String> formIds = files.stream().map(FileDetailChilDto::getId).collect(Collectors.toList());
    //删除数据
    for (final String dbId : dbIds) {
      if (!formIds.contains(dbId)) {
        this.removeFileDetailById(dbId);
      }
    }
  }

}
