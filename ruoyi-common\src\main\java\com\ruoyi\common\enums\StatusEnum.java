package com.ruoyi.common.enums;

/**
 * @author: wang<PERSON><PERSON>
 * @create: 2024/8/9/0009
 * @Description:
 * @FileName: ExceptionEnum
 */


/**
 * @description: 异常处理枚举类
 * @author: DT
 * @date: 2021/4/19 21:41
 * @version: v1.0
 */
public enum StatusEnum {
  SUCCESS(200, "请求处理成功"),
  VALIDATED(500, "请求处理成功"),
  UNAUTHORIZED(401, "用户认证失败"),
  FORBIDDEN(403, "权限不足"),
  SERVICE_ERROR(500, "服务器去旅行了，请稍后重试"),
  PARAM_INVALID(1000, "无效的参数"),
  // 数据操作错误定义
  BODY_NOT_MATCH(4000, "请求的数据格式不符!"),
  SIGNATURE_NOT_MATCH(4001, "请求的数字签名不匹配!"),
  NOT_FOUND(4004, "未找到该资源!"),
  INTERNAL_SERVER_ERROR(5000, "服务器内部错误!"),
  SERVER_BUSY(5003, "服务器正忙，请稍后再试!");

  /**
   * 错误码
   */
  private final Integer resultCode;

  /**
   * 错误描述
   */
  private final String resultMsg;

  StatusEnum(final Integer resultCode, final String resultMsg) {
    this.resultCode = resultCode;
    this.resultMsg = resultMsg;
  }

  public Integer getResultCode() {
    return resultCode;
  }

  public String getResultMsg() {
    return resultMsg;
  }
}
