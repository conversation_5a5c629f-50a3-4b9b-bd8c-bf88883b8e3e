package com.ruoyi.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 标签详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@TableName("label_detail")
@ApiModel(value = "LabelDetailZh对象", description = "标签详情")
public class LabelDetail implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(type = IdType.ASSIGN_UUID)
  private String id;
  @ApiModelProperty("标签ID")
  @TableField("label_id")
  private String labelId;
  @ApiModelProperty("语种")
  @TableField("lang_type")
  private String langType;
  @ApiModelProperty("标签名称")
  @TableField("label_name")
  private String labelName;

  @ApiModelProperty("标签分类")
  @TableField("label_type")
  private String labelType;

  @ApiModelProperty("备用字段")
  @TableField("remark1")
  private String remark1;


}




