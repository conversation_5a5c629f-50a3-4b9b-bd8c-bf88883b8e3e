package com.ruoyi.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Data
@TableName("type_manage")
@ApiModel(value = "TypeManage对象", description = "")
public class TypeManage implements Serializable {

  private static final long serialVersionUID = 1L;
  
  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  @ApiModelProperty("名称")
  @TableField("name")
  @NotBlank(message = "名称不能为空")
  @Size(min = 0, max = 100, message = "岗位编码长度不能超过100个字符")
  private String name;

  @ApiModelProperty("分类id/切换语言用id")
  @TableField("type_id")
  private String typeId;

  @ApiModelProperty("描述")
  @TableField("descrition")
  @NotBlank(message = "描述不能为空")
  @Size(min = 0, max = 500, message = "岗位编码长度不能超过500个字符")
  private String descrition;

  @ApiModelProperty("色值")
  @TableField("color_value")
  private String colorValue;

  @ApiModelProperty("一级类别id")
  @TableField("menu_id")
  private String menuId;

  @ApiModelProperty("一级类别名称")
  @TableField("menu_name")
  private String menuName;

  @ApiModelProperty("排序")
  @TableField("sort")
  private Integer sort;
  @ApiModelProperty("管理种类")
  @NotBlank(message = "管理种类不能为空")
  @TableField("manage_type")
  private String manageType;
  @ApiModelProperty("语言")
  @TableField("lang_type")
  private String langType;
  @ApiModelProperty("创建时间")
  @TableField("created_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date createdTime;
  @ApiModelProperty("修改时间")
  @TableField("updated_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date updatedTime;

  @TableField(exist = false)
  private String nameEn;

  @TableField(exist = false)
  private String nameJa;

  @TableField(exist = false)
  private String nameZh;

}




