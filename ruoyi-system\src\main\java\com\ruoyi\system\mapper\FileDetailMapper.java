package com.ruoyi.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import com.ruoyi.system.entity.FileDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 文件详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Mapper
public interface FileDetailMapper extends BaseMapper<FileDetail> {

  List<FileDetailChilDto> selectFileList(@Param("productId") String productId);

  List<FileDetailChilDto> selectSuperFileList(@Param("productId") String productId);

  Integer selectMaxSort();


  void updateSort(@Param("id") String id);

  List<FileDetailChilDto> selectSuperProductFileList(String productId);
}
