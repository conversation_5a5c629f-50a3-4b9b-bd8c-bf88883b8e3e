package com.ruoyi.system.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.dto.FileDetailChilDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商品详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@TableName("product_detail")
@ApiModel(value = "ProductDetailZh对象", description = "商品详情表 ")
public class ProductDetail implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("ID")
  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  @ApiModelProperty("名称")
  @TableField("name")
  @NotBlank(message = "名称不能为空")
  @Size(min = 0, max = 100, message = "名称长度不能超过100个字符")
  private String name;

  @ApiModelProperty("品相")
  @TableField("appearance")
  @NotBlank(message = "品相不能为空")
  @Size(min = 0, max = 100, message = "品相长度不能超过100个字符")
  private String appearance;

  @ApiModelProperty("商品编号")
  @TableField("product_no")
  @NotBlank(message = "商品编号不能为空")
  @Size(min = 0, max = 100, message = "商品编号长度不能超过100个字符")
  private String productNo;

  @ApiModelProperty("颜色")
  @TableField("color")
  @NotBlank(message = "颜色不能为空")
  @Size(min = 0, max = 100, message = "颜色长度不能超过100个字符")
  private String color;

  @ApiModelProperty("材质")
  @TableField("material")
  @NotBlank(message = "材质不能为空")
  @Size(min = 0, max = 100, message = "材质长度不能超过100个字符")
  private String material;

  @ApiModelProperty("金属件")
  @TableField("metal_parts")
  @NotBlank(message = "金属件不能为空")
  @Size(min = 0, max = 100, message = "金属件不能超过100个字符")
  private String metalParts;
  @TableField(exist = false)
  private String metalPartsZh;
  @TableField(exist = false)
  private String metalPartsJa;
  @TableField(exist = false)
  private String metalPartsEn;

  @ApiModelProperty("刻印")
  @TableField("engraving")
  @NotBlank(message = "刻印不能为空")
  @Size(min = 0, max = 100, message = "刻印不能超过100个字符")
  private String engraving;

  @ApiModelProperty("尺寸")
  @TableField("product_size")
  @NotBlank(message = "尺寸不能为空")
  @Size(min = 0, max = 100, message = "尺寸不能超过100个字符")
  private String productSize;

  @ApiModelProperty("配件")
  @TableField("accessories")
  @NotBlank(message = "配件不能为空")
  @Size(min = 0, max = 100, message = "配件不能超过100个字符")
  private String accessories;
  @TableField(exist = false)
  private String accessoriesZh;
  @TableField(exist = false)
  private String accessoriesJa;
  @TableField(exist = false)
  private String accessoriesEn;

  @ApiModelProperty("款式")
  @TableField("product_style")
  @NotBlank(message = "款式不能为空")
  @Size(min = 0, max = 100, message = "款式不能超过100个字符")

  private String productStyle;

  @ApiModelProperty("类别")
  @TableField("category")
  @NotBlank(message = "类别不能为空")
  private String category;

  @ApiModelProperty("别称")
  @TableField("another_name")
  private String anotherName;

  @ApiModelProperty("成品描述")
  @TableField("descrition")
  private String descrition;

  @ApiModelProperty("创建时间")
  @TableField("created_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date createdTime;

  @ApiModelProperty("创建人")
  @TableField("created_user")
  private String createdUser;

  @ApiModelProperty("修改时间")
  @TableField("updated_time")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
  @JsonFormat(
      pattern = "yyyy-MM-dd HH:mm",
      timezone = "GMT+8")
  private Date updatedTime;

  @ApiModelProperty("修改人")
  @TableField("updated_user")
  private String updatedUser;

  @ApiModelProperty("是否删除")
  @TableField("is_delete")
  private Integer isDelete;

  @ApiModelProperty("中文价格")
  @TableField("china_price")
  @NotBlank(message = "中文价格不能为空")
//  @Size(min = 0, max = 8, message = "中文价格不能超过8位数")
  private String chinaPrice;

  @ApiModelProperty("英文价格")
  @TableField("eng_price")
  @NotBlank(message = "英文价格不能为空")
//  @Size(min = 0, max = 8, message = "英文价格不能超过8位数")
  private String engPrice;
  @ApiModelProperty("日文价格")
  @TableField("jap_price")
  @NotBlank(message = "日文价格不能为空")
//  @Size(min = 0, max = 8, message = "日文价格不能超过8位数")
  private String japPrice;

  @TableField(exist = false)
  private List<FileDetailChilDto> fileUrls;
  @TableField(exist = false)
  private List<FileDetailChilDto> superFileUrls;
  @ApiModelProperty("商品ID")
  @TableField("product_id")
  private String productId;

  @ApiModelProperty("语种")
  @TableField("lang_type")
  private String langType;

  @ApiModelProperty("一级类别")
  @TableField("menu_id")
  private String menuId;

  @ApiModelProperty("是否展示 1展示 2不展示")
  @TableField("is_show")
  @NotBlank(message = "是否展示不能为空")
  private Integer isShow;

  @TableField(exist = false)
  private List<String> dictDataList;

  @TableField(exist = false)
  private List<FileDetailChilDto> superProdrctFileUrls;

}




