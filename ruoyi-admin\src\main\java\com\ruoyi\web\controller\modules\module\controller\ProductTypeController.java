package com.ruoyi.web.controller.modules.module.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.MenuDetail;
import com.ruoyi.system.service.IMenuDetailService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: wang<PERSON><PERSON>
 * @create: 2024/9/6/0006
 * @Description:
 * @FileName: lunbotuController
 */
@RestController
@RequestMapping("/product-type")
@Api(tags = "商品类别", description = "商品类别管理")
public class ProductTypeController extends BaseController {

  @Autowired
  private IMenuDetailService iMenuDetailService;

  @GetMapping("/getMenuList")
  @ApiOperation(value = "下拉列表 1- 一级 2- 二级 3-三级下拉")
  public R<List<TopMenuVO>> getMenuList(final Integer menuType, final String parentId) throws TencentCloudSDKException, InterruptedException {
    final List<TopMenuVO> topMenuVOS = iMenuDetailService.getMenuList(menuType, parentId);
    if (topMenuVOS != null) {
      return R.ok(topMenuVOS);
    } else {
      return R.fail("操作失败");
    }
  }


  @PostMapping("/saveOrUpdate")
  @ApiOperation(value = "新增或者修改商品类别")
  public R<Boolean> saveMenuDetail(@RequestBody final MenuDetail menuDetail) throws TencentCloudSDKException, InterruptedException {
    if (iMenuDetailService. saveMenuDetail(menuDetail)) {
      return R.ok();
    } else {
      return R.fail("操作失败");
    }

  }

  @GetMapping("/getDetail")
  @ApiOperation(value = "查询商品类别详情")
  public R<MenuDetail> getDetail(final String id) {
    final MenuDetail menuDetail = iMenuDetailService.getDetail(id);
    if (menuDetail != null) {
      return R.ok(menuDetail);
    } else {
      return R.fail("操作失败");
    }

  }


  @DeleteMapping("/delete/{id}")
  @ApiOperation(value = "删除分类")
  public R<Boolean> delete(@PathVariable final String id) throws TencentCloudSDKException, InterruptedException {
    if (iMenuDetailService.deleteById(id)) {
      return R.ok();
    } else {
      return R.fail("操作失败");
    }
  }


  @GetMapping("/page")
  @Anonymous
  @ApiOperation(value = "分页查询类别详情")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "menuId", value = "父级Id", required = false, dataType = "String")
  })
  public R<TableDataInfo> findMenuDetailByPage(
      final String menuId) {
    startPage();
    final List<MenuDetail> productDetailByPage = iMenuDetailService.findMenuDetailByPage(menuId);
    final TableDataInfo dataTable = getDataTable(productDetailByPage);
    if (productDetailByPage != null) {
      return R.ok(dataTable);
    } else {
      return R.fail("操作失败");
    }
  }
}
