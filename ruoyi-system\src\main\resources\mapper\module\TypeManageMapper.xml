<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TypeManageMapper">






    <select id="selectMenuList" resultType="com.ruoyi.system.domain.vo.TopMenuVO">
        select menu_id as value, menu_name as label,sort, updated_time, created_time
        from type_manage
        where menu_id is not null
        GROUP BY menu_id, menu_name,sort, updated_time, created_time
        ORDER BY sort ASC, updated_time DESC, created_time DESC;
    </select>

    <select id="selectChildList" resultType="com.ruoyi.system.domain.vo.TopMenuVO">
        SELECT type_id AS value, name AS label, sort, updated_time, created_time
        FROM type_manage
        where menu_id = #{value}
          AND lang_type = 'zh'
        GROUP BY type_id, name, sort, updated_time, created_time
        ORDER BY sort ASC, updated_time DESC, created_time DESC;
    </select>

    <update id="updateSort">
        update type_manage
        set sort = null
        where type_id = #{typeId}
    </update>
</mapper>
