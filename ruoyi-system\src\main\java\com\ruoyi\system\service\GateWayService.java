package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.domain.dto.HomeScreenDTO;
import com.ruoyi.system.domain.vo.ProductDetailVO;
import com.ruoyi.system.entity.ProductDetail;

import java.util.List;
import java.util.Map;

public interface GateWayService {
  /**
   * 头部菜单
   *
   * @param lang
   * @return
   */
  R<List<Map<String, Object>>> productListTop(String lang);

  /**
   * 首页筛选框内容
   *
   * @param lang
   * @param menuId
   * @return
   */
  R<Map<String, Object>> screenList(String lang, String menuId);

  List<ProductDetailVO> productList(HomeScreenDTO homeScreenDTO);
}
