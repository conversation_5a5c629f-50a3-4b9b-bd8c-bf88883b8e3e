package com.ruoyi.web.controller.common;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtilCommons;
import com.ruoyi.framework.config.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController {
  private static final Logger log = LoggerFactory.getLogger(CommonController.class);

  @Autowired
  private ServerConfig serverConfig;

  private static final String FILE_DELIMETER = ",";

  /**
   * 通用下载请求
   *
   * @param fileName 文件名称
   * @param delete   是否删除
   */
  @GetMapping("/download")
  public void fileDownload(final String fileName, final Boolean delete, final HttpServletResponse response, final HttpServletRequest request) {
    try {
      if (!FileUtilCommons.checkAllowDownload(fileName)) {
        throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
      }
      final String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
      final String filePath = RuoYiConfig.getDownloadPath() + fileName;

      response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
      FileUtilCommons.setAttachmentResponseHeader(response, realFileName);
      FileUtilCommons.writeBytes(filePath, response.getOutputStream());
      if (delete) {
        FileUtilCommons.deleteFile(filePath);
      }
    } catch (final Exception e) {
      log.error("下载文件失败", e);
    }
  }

  /**
   * 通用上传请求（单个）
   */
  @PostMapping("/upload")
  public AjaxResult uploadFile(final MultipartFile file) throws Exception {
    try {
      // 上传文件路径
      final String filePath = RuoYiConfig.getUploadPath();
      // 上传并返回新文件名称
      final String fileName = FileUploadUtils.upload(filePath, file);
      final String url = serverConfig.getUrl() + fileName;
      final AjaxResult ajax = AjaxResult.success();
      ajax.put("url", url);
      ajax.put("fileName", fileName);
      ajax.put("newFileName", FileUtilCommons.getName(fileName));
      ajax.put("originalFilename", file.getOriginalFilename());
      return ajax;
    } catch (final Exception e) {
      return AjaxResult.error(e.getMessage());
    }
  }

  /**
   * 通用上传请求（多个）
   */
  @PostMapping("/uploads")
  public AjaxResult uploadFiles(final List<MultipartFile> files) throws Exception {
    try {
      // 上传文件路径
      final String filePath = RuoYiConfig.getUploadPath();
      final List<String> urls = new ArrayList<String>();
      final List<String> fileNames = new ArrayList<String>();
      final List<String> newFileNames = new ArrayList<String>();
      final List<String> originalFilenames = new ArrayList<String>();
      for (final MultipartFile file : files) {
        // 上传并返回新文件名称
        final String fileName = FileUploadUtils.upload(filePath, file);
        final String url = serverConfig.getUrl() + fileName;
        urls.add(url);
        fileNames.add(fileName);
        newFileNames.add(FileUtilCommons.getName(fileName));
        originalFilenames.add(file.getOriginalFilename());
      }
      final AjaxResult ajax = AjaxResult.success();
      ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
      ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
      ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
      ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
      return ajax;
    } catch (final Exception e) {
      return AjaxResult.error(e.getMessage());
    }
  }

  /**
   * 本地资源通用下载
   */
  @GetMapping("/download/resource")
  public void resourceDownload(final String resource, final HttpServletRequest request, final HttpServletResponse response)
      throws Exception {
    try {
      if (!FileUtilCommons.checkAllowDownload(resource)) {
        throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
      }
      // 本地资源路径
      final String localPath = RuoYiConfig.getProfile();
      // 数据库资源地址
      final String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
      // 下载名称
      final String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
      response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
      FileUtilCommons.setAttachmentResponseHeader(response, downloadName);
      FileUtilCommons.writeBytes(downloadPath, response.getOutputStream());
    } catch (final Exception e) {
      log.error("下载文件失败", e);
    }
  }
}
