package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.StatusEnum;
import com.ruoyi.common.enumutil.LanguageEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.resolver.TencentTranslationClient;
import com.ruoyi.common.utils.MinioUtils;
import com.ruoyi.system.domain.dto.TranslatedReqVO;
import com.ruoyi.system.domain.vo.ProductDetailVO;
import com.ruoyi.system.entity.*;
import com.ruoyi.system.mapper.FileDetailMapper;
import com.ruoyi.system.mapper.LabelDetailMapper;
import com.ruoyi.system.mapper.MenuDetailMapper;
import com.ruoyi.system.mapper.ProductDetailMapper;
import com.ruoyi.system.service.IFileDetailService;
import com.ruoyi.system.service.IProductDetailService;
import com.ruoyi.system.service.IProductWithLabelService;
import com.ruoyi.system.service.TypeManageService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.StringUtils.getUUID;
import static com.ruoyi.common.utils.StringUtils.isBlankString;


/**
 * <p>
 * 商品详情表  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Service
public class ProductDetailServiceImpl extends ServiceImpl<ProductDetailMapper, ProductDetail> implements IProductDetailService {

  @Value("${baidu-key.secretId}")
  private String secretId;
  @Value("${baidu-key.secretKey}")
  private String secretKey;
  @Autowired
  private ProductDetailMapper productDetailMapper;

  @Autowired
  private MinioUtils minioUtils;
  @Autowired
  private FileDetailMapper fileDetailMapper;

  @Autowired
  private LabelDetailMapper labelDetailMapper;
  @Autowired
  private IFileDetailService iFileDetailService;
  @Autowired
  private IProductWithLabelService iProductWithLabelService;
  @Autowired
  private TypeManageService typeManageService;


  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean saveOrUpdateProductDetail(final ProductDetail productDetail) throws TencentCloudSDKException, InterruptedException {
    boolean save = false;
    productDetail.setLangType(LanguageEnum.CHINA.getValue());
    if (productDetail == null || !productDetail.getLangType().equals(LanguageEnum.CHINA.getValue())) {
      throw new ServiceException("数据有误", StatusEnum.SERVICE_ERROR.getResultCode());
    }
    final List<MenuDetail> menuDetails = menuDetailMapper.selectList(new QueryWrapper<MenuDetail>().eq("MENU_ID", productDetail.getCategory()).eq("lang_type", "zh"));
//    //校验不同类别下的商品名称 新增
//    final ProductDetail productDetailOther =
//        baseMapper.selectOne(
//            new QueryWrapper<ProductDetail>()
//                .eq("name", productDetail.getName())
//                .eq("category", productDetail.getCategory())
//                .eq("lang_type", "zh"));
//    //新增
//    if (StringUtils.isBlank(productDetail.getProductId()) && productDetailOther != null) {
//      throw new ServiceException("当前类别下商品名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
//
//    } else if (StringUtils.isNotBlank(productDetail.getProductId())
//        && productDetailOther != null
//        && !productDetailOther.getProductId().equals(productDetail.getProductId())) {
//      throw new ServiceException("当前类别下商品名称已存在", StatusEnum.SERVICE_ERROR.getResultCode());
//
//    }

    //修改中文信息
    if (StringUtils.isBlank(productDetail.getProductId())) {
      productDetail.setProductId(getUUID());
      productDetail.setCreatedTime(new Date());
      productDetail.setUpdatedTime(new Date());
      productDetail.setMenuId(menuDetails.get(0).getParentId());
      productDetail.setMetalParts(productDetail.getMetalPartsZh());
      productDetail.setAccessories(productDetail.getAccessoriesZh());
      baseMapper.insert(productDetail);
      final ProductDetail productEnTarget = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.EN.getValue(), productDetail);
      final ProductDetail productJaTarget = translation(LanguageEnum.CHINA.getValue(), LanguageEnum.JA.getValue(), productDetail);
      productJaTarget.setMetalParts(productDetail.getMetalPartsJa());
      productJaTarget.setAccessories(productDetail.getAccessoriesJa());
      productEnTarget.setMetalParts(productDetail.getMetalPartsEn());
      productEnTarget.setAccessories(productDetail.getAccessoriesEn());
      baseMapper.insert(productEnTarget);
      save = baseMapper.insert(productJaTarget) > 0;
    } else {
      productDetail.setMenuId(menuDetails.get(0).getParentId());
      productDetail.setUpdatedTime(new Date());
      final List<ProductDetail> menuId = baseMapper.selectList(new QueryWrapper<ProductDetail>().eq("product_id", productDetail.getProductId()));
      for (final ProductDetail productDetail1 : menuId) {
        final String id = productDetail1.getId();
        if (productDetail1.getLangType().equals("zh")) {
          BeanUtils.copyProperties(productDetail, productDetail1);
          productDetail1.setMetalParts(productDetail.getMetalPartsZh());
          productDetail1.setAccessories(productDetail.getAccessoriesZh());
        } else {
          final ProductDetail translation = translation(LanguageEnum.CHINA.getValue(), productDetail1.getLangType(), productDetail);

          BeanUtils.copyProperties(translation, productDetail1);
          if (LanguageEnum.EN.getValue().equals(productDetail1.getLangType())) {
            productDetail1.setMetalParts(productDetail.getMetalPartsEn());
            productDetail1.setAccessories(productDetail.getAccessoriesEn());
          } else if (LanguageEnum.JA.getValue().equals(productDetail1.getLangType())) {
            productDetail1.setMetalParts(productDetail.getMetalPartsJa());
            productDetail1.setAccessories(productDetail.getAccessoriesJa());
          }
        }
        //修改
        productDetail1.setId(id);
        baseMapper.updateById(productDetail1);
      }
    }
    //更新文件 文件修改 先mv文件
    if (productDetail.getFileUrls() != null && productDetail.getFileUrls().size() > 0) {
      final List<FileDetail> collect = productDetail.getFileUrls().stream().map(item -> {
        final FileDetail fileDetail = fileDetailMapper.selectById(item.getId());
        fileDetail.setProductId(productDetail.getProductId());
        fileDetail.setIsCarousel(0);
        fileDetail.setFileUrl(minioUtils.mvFile(fileDetail.getProductId(), fileDetail.getFileName()));
        return fileDetail;
      }).collect(Collectors.toList());
      save = iFileDetailService.saveOrUpdateBatch(collect);
      if (!save) {
        throw new ServiceException("绑定图片信息失败", StatusEnum.SERVICE_ERROR.getResultCode());
      }
    }
    //标签修改
    if (productDetail.getDictDataList() != null && productDetail.getDictDataList().size() > 0) {
      final List<ProductWithLabel> collect = productDetail.getDictDataList().stream().map(item -> {
        final ProductWithLabel productWithLabel = new ProductWithLabel();
        productWithLabel.setProductId(productDetail.getProductId());
        productWithLabel.setLabelId(item);
        return productWithLabel;
      }).collect(Collectors.toList());
      iProductWithLabelService.remove(new QueryWrapper<ProductWithLabel>().eq("product_id", productDetail.getProductId()));
      save = iProductWithLabelService.saveBatch(collect);

    }
    //超级单品文件修改
    if (productDetail.getSuperFileUrls() != null && productDetail.getSuperFileUrls().size() > 0) {
      final List<FileDetail> collect = productDetail.getSuperFileUrls().stream().map(item -> {
        final FileDetail fileDetail = fileDetailMapper.selectById(item.getId());
        fileDetail.setProductId(productDetail.getProductId());
        fileDetail.setIsCarousel(3);
        fileDetail.setFileUrl(minioUtils.mvFile(fileDetail.getProductId() + "/supper", fileDetail.getFileName()));
        return fileDetail;
        // Todo 删除
      }).collect(Collectors.toList());
      save = iFileDetailService.saveOrUpdateBatch(collect);
    }
    //超级单品商品文件修改
    if (productDetail.getSuperProdrctFileUrls() != null && productDetail.getSuperProdrctFileUrls().size() > 0) {
      final List<FileDetail> collect = productDetail.getSuperProdrctFileUrls().stream().map(item -> {
        final FileDetail fileDetail = fileDetailMapper.selectById(item.getId());
        fileDetail.setProductId(productDetail.getProductId());
        fileDetail.setIsCarousel(4);
        fileDetail.setFileUrl(minioUtils.mvFile(fileDetail.getProductId() + "/supper-product", fileDetail.getFileName()));
        return fileDetail;
        // Todo 删除
      }).collect(Collectors.toList());
      save = iFileDetailService.saveOrUpdateBatch(collect);
    }
    //删除赘余文件
    iFileDetailService.updateFiles(productDetail.getProductId(), productDetail.getFileUrls(), 0);
    if (productDetail.getSuperFileUrls()!=null) {
      iFileDetailService.updateFiles(productDetail.getProductId(), productDetail.getSuperFileUrls(), 3);
    }
    if (productDetail.getSuperProdrctFileUrls()!=null){
      iFileDetailService.updateFiles(productDetail.getProductId(), productDetail.getSuperProdrctFileUrls(), 4);
    }
    return save;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean removeProductDetailById(final String id) {
    //删除中日英所有商品信息
    final ProductDetail productDetail = productDetailMapper.selectById(id);
    productDetailMapper.delete(new QueryWrapper<ProductDetail>().eq("product_id", productDetail.getProductId()));
    final int productId = fileDetailMapper.delete(new QueryWrapper<FileDetail>().eq("product_id", productDetail.getProductId()));
    return productId > 0;
  }

  @Override
  public List<ProductDetailVO> getProductDetailByPage(final String productName, final String color, final String appearance, final String material, final String category, final Integer isShow) {
    final QueryWrapper<ProductDetail> queryWrapper = new QueryWrapper<>();
    queryWrapper.like(StringUtils.isNotBlank(productName), "name", productName).or().like(StringUtils.isNotBlank(productName), "another_name", productName);
    queryWrapper.eq(StringUtils.isNotBlank(appearance), "appearance", appearance)
            .eq(StringUtils.isNotBlank(color), "color", color)
            .eq(StringUtils.isNotBlank(category), "category", category)
            .eq(StringUtils.isNotBlank(material), "material", material)
            .eq(isShow != null, "is_show", isShow)
            .eq("lang_type", LanguageEnum.CHINA.getValue())
            .orderByDesc("updated_time").orderByDesc("created_time");

    final List<ProductDetail> productDetailIPage = productDetailMapper.selectList(queryWrapper);
    final List<ProductDetailVO> productDetailVOS = productDetailIPage.stream().map(item -> {
      final ProductDetailVO productDetail = getProductTypeDetail(item.getProductId(), "zh");
      productDetail.setFileUrls(fileDetailMapper.selectFileList(item.getProductId()));
      return productDetail;
    }).collect(Collectors.toList());
    return productDetailVOS;
  }

  @Override
  public String japanToChinese(final TranslatedReqVO req) {
    final TencentTranslationClient tencentTranslationClient = new TencentTranslationClient();
    try {
      final String translatedText = tencentTranslationClient.translateText(req.getStr(), req.getSourceLang(), req.getTargetLang());
      return translatedText;
    } catch (final TencentCloudSDKException e) {
      throw new ServiceException(e.getMessage());
    }
  }

  @Override
  public ProductDetailVO findAllProductDetail(final String productId, final String langType) {
    final ProductDetailVO one = getProductTypeDetail(productId, LanguageEnum.CHINA.getValue());
    final ProductDetailVO ja = getProductTypeDetail(productId, LanguageEnum.JA.getValue());
    final ProductDetailVO en = getProductTypeDetail(productId, LanguageEnum.EN.getValue());
    one.setAccessoriesZh(one.getAccessories());
    one.setAccessoriesEn(en.getAccessories());
    one.setAccessoriesJa(ja.getAccessories());
    one.setMetalPartsZh(one.getMetalParts());
    one.setMetalPartsEn(en.getMetalParts());
    one.setMetalPartsJa(ja.getMetalParts());
    one.setFileUrls(fileDetailMapper.selectFileList(one.getProductId()));
    one.setSuperFileUrls(fileDetailMapper.selectSuperFileList(one.getProductId()));
    one.setDictDataList(labelDetailMapper.selectLabelList(one.getProductId()));
    one.setSuperProdrctFileUrls(fileDetailMapper.selectSuperProductFileList(one.getProductId()));
    return one;
  }

  @Autowired
  MenuDetailMapper menuDetailMapper;

  @Override
  public ProductDetailVO getProductTypeDetail(final String productId, final String langType) {
    ProductDetail one = baseMapper.selectOne(new QueryWrapper<ProductDetail>().eq("product_id", productId).eq("lang_type", langType));
    if (one == null || StringUtils.isBlank(one.getId())) {
      one = baseMapper.selectOne(new QueryWrapper<ProductDetail>().eq("id", productId).eq("lang_type", langType));
    }
    final ProductDetailVO productDetailVO = new ProductDetailVO();
    BeanUtils.copyProperties(one, productDetailVO);
    productDetailVO.setCategoryName(one.getCategory() == null ? null : menuDetailMapper.selectContactMenuName(one.getCategory(), langType));
    productDetailVO.setMaterialName(one.getMaterial() == null ? null : typeManageService.getOne(new QueryWrapper<TypeManage>().eq("type_id", one.getMaterial()).eq("lang_type", langType)).getName());
    productDetailVO.setAppearanceName(one.getAppearance() == null ? null : typeManageService.getOne(new QueryWrapper<TypeManage>().eq("type_id", one.getAppearance()).eq("lang_type", langType)).getName());
    productDetailVO.setColorName(one.getColor() == null ? null : typeManageService.getOne(new QueryWrapper<TypeManage>().eq("type_id", one.getColor()).eq("lang_type", langType)).getName());
    productDetailVO.setIsShowName(one.getIsShow().equals(1) ? "上架" : "下架");

    return productDetailVO;
  }

  /**
   * 翻译商品类
   *
   * @param sourceLang
   * @param targetLang
   * @param productSource
   * @return
   */
  ProductDetail translation(final String sourceLang, final String targetLang, final ProductDetail productSource) throws TencentCloudSDKException {
    final TencentTranslationClient tencentTranslationClient = new TencentTranslationClient();
    final ProductDetail productTarget = new ProductDetail();
    BeanUtils.copyProperties(productSource, productTarget);
    productTarget.setId(null);
    productTarget.setAnotherName(isBlankString(productSource.getAnotherName()) ? null : tencentTranslationClient.translateText(productSource.getAnotherName(), sourceLang, targetLang));
    productTarget.setName(isBlankString(productSource.getName()) ? null : tencentTranslationClient.translateText(productSource.getName(), sourceLang, targetLang));
    productTarget.setDescrition(isBlankString(productSource.getDescrition()) ? null : tencentTranslationClient.translateText(productSource.getDescrition(), sourceLang, targetLang));
    productTarget.setMetalParts(isBlankString(productSource.getMetalParts()) ? null : tencentTranslationClient.translateText(productSource.getMetalParts(), sourceLang, targetLang));
    productTarget.setAccessories(isBlankString(productSource.getAccessories()) ? null : tencentTranslationClient.translateText(productSource.getAccessories(), sourceLang, targetLang));
    productTarget.setProductNo(isBlankString(productSource.getProductNo()) ? null : tencentTranslationClient.translateText(productSource.getProductNo(), sourceLang, targetLang));
    productTarget.setEngraving(isBlankString(productSource.getEngraving()) ? null : tencentTranslationClient.translateText(productSource.getEngraving(), sourceLang, targetLang));
    productTarget.setProductSize(isBlankString(productSource.getProductSize()) ? null : tencentTranslationClient.translateText(productSource.getProductSize(), sourceLang, targetLang));
    productTarget.setLangType(targetLang);
    productTarget.setCreatedTime(new Date());
    return productTarget;
  }


}
