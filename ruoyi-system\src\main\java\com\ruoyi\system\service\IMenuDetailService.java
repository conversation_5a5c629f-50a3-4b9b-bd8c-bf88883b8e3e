package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.MenuDetail;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface IMenuDetailService extends IService<MenuDetail> {

  boolean saveMenuDetail(MenuDetail menuDetail) throws TencentCloudSDKException, InterruptedException;


  boolean deleteById(String menuId);

  List<MenuDetail> findMenuDetailByPage(String menuName);

  MenuDetail getDetail(String id);


  List<TopMenuVO> getMenuList(Integer menuType, String parentId);
}
