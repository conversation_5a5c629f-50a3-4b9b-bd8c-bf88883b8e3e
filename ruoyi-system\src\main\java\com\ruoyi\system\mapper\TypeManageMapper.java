package com.ruoyi.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.vo.TopMenuVO;
import com.ruoyi.system.entity.TypeManage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Mapper
public interface TypeManageMapper extends BaseMapper<TypeManage> {

  List<TopMenuVO> selectMenuList();


  List<TopMenuVO> selectChildList(@Param("value") String value);

  void updateSort(@Param("typeId") String typeId);
}
