package com.ruoyi.web.controller.modules.module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.entity.LabelDetail;
import com.ruoyi.system.service.ILabelDetailService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 标签详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@RestController
@RequestMapping("/label-detail")
@Api(tags = "标签详情", description = "标签详情")
public class LabelDetailController extends BaseController {

  @Autowired
  private ILabelDetailService labelDetailService;

  @PostMapping("/save")
  public R<Boolean> saveLabelDetail(@RequestBody final LabelDetail labelDetail) throws InterruptedException, TencentCloudSDKException {
    if (labelDetailService.saveOrUpdateLabelDetail(labelDetail)) {
      return R.ok();
    } else {
      return R.fail("操作失败");

    }
  }

  @DeleteMapping("/delete/{id}")
  public R<Boolean> deleteLabelDetailById(@PathVariable final Integer id) {
    if (labelDetailService.remove(new QueryWrapper<LabelDetail>().eq("label_id", id))) {
      return R.ok();
    } else {
      return R.fail("操作失败");

    }

  }

  @GetMapping("/{id}")
  public R<LabelDetail> findOneLabelDetailById(@PathVariable("id") final Integer id, final String langType) {
    final LabelDetail one = labelDetailService.getOne(new QueryWrapper<LabelDetail>().eq("label_id", id).eq("lang_type", langType));
    if (one != null) {
      return R.ok(one);
    } else {
      return R.fail("操作失败");

    }
  }

  @GetMapping("/page")
  public R<TableDataInfo> findLabelDetailByPage(final Integer currentPage,
                                                final Integer pageSize,
                                                final String langType,
                                                final String labelDetailname) {
    startPage();
    final List<LabelDetail> labelDetailByPage = labelDetailService.getLabelDetailByPage(currentPage, pageSize, labelDetailname, langType);
    final TableDataInfo dataTable = getDataTable(labelDetailByPage);
    if (labelDetailByPage != null && labelDetailByPage.size() >= 0) {
      return R.ok(dataTable);
    } else {
      return R.fail("操作失败");

    }
  }

}

