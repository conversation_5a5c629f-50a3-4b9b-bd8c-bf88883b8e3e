package com.ruoyi.system.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 文件详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@ApiModel(value = "FileDetail对象", description = "文件详情")
public class FileDetailChilDto implements Serializable {

  private static final long serialVersionUID = -4106609770266647206L;

  @ApiModelProperty("图片跳转地址")
  private String url;

  @ApiModelProperty("文件id")
  private String id;

}




